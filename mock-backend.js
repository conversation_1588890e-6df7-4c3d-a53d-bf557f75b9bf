const express = require('express');
const cors = require('cors');
const app = express();
const port = 8080;

// Enable CORS for all routes
app.use(cors());
app.use(express.json());

// Mock data
const mockAssessments = [
  {
    id: 1,
    equipmentId: "TANK-001",
    assessmentType: "PART_4_GENERAL_METAL_LOSS",
    status: "COMPLETED",
    createdDate: "2024-01-15",
    result: "ACCEPTABLE"
  },
  {
    id: 2,
    equipmentId: "PIPE-002", 
    assessmentType: "PART_5_LOCAL_THIN_AREAS",
    status: "IN_PROGRESS",
    createdDate: "2024-01-16",
    result: "PENDING"
  }
];

const mockMaterials = [
  {
    id: 1,
    name: "SA-516 Grade 70",
    specification: "SA-516",
    grade: "70",
    yieldStrength: 262.0,
    tensileStrength: 485.0,
    allowableStress: 138.0
  },
  {
    id: 2,
    name: "SA-285 Grade C",
    specification: "SA-285", 
    grade: "C",
    yieldStrength: 207.0,
    tensileStrength: 380.0,
    allowableStress: 95.0
  }
];

const mockStatistics = {
  totalAssessments: 25,
  completedAssessments: 18,
  pendingAssessments: 7,
  acceptableResults: 15,
  unacceptableResults: 3
};

// API Routes
app.get('/api/v1/assessments/statistics', (req, res) => {
  res.json(mockStatistics);
});

app.get('/api/v1/assessments', (req, res) => {
  res.json(mockAssessments);
});

app.get('/api/v1/assessments/:id', (req, res) => {
  const assessment = mockAssessments.find(a => a.id == req.params.id);
  if (assessment) {
    res.json(assessment);
  } else {
    res.status(404).json({ error: 'Assessment not found' });
  }
});

app.post('/api/v1/assessments', (req, res) => {
  const newAssessment = {
    id: mockAssessments.length + 1,
    ...req.body,
    status: 'IN_PROGRESS',
    createdDate: new Date().toISOString().split('T')[0]
  };
  mockAssessments.push(newAssessment);
  res.status(201).json(newAssessment);
});

app.get('/api/v1/materials', (req, res) => {
  res.json(mockMaterials);
});

app.get('/api/v1/materials/:id', (req, res) => {
  const material = mockMaterials.find(m => m.id == req.params.id);
  if (material) {
    res.json(material);
  } else {
    res.status(404).json({ error: 'Material not found' });
  }
});

// FFS Calculation endpoints implementing actual API 579-1 formulas
app.post('/api/v1/calculations/part4/level1', (req, res) => {
  const {
    thickness,
    corrosionRate,
    designPressure,
    allowableStress,
    insideDiameter = 60.0, // inches, default if not provided
    weldEfficiency = 1.0,  // default if not provided
    futureCorrosionAllowance = 0.1, // inches, default if not provided
    assessmentLife = 10 // years, default if not provided
  } = req.body;

  try {
    // API 579-1 Part 4 Level 1 calculation
    const radius = insideDiameter / 2.0;

    // Step 1: Calculate minimum required thickness using ASME formula
    // Formula: t_req = (P * R) / (S * E - 0.6 * P)
    const treq = (designPressure * radius) / (allowableStress * weldEfficiency - 0.6 * designPressure);

    // Step 2: Calculate minimum thickness for continued operation
    // tcmin = treq + FCA
    const tcmin = treq + futureCorrosionAllowance;

    // Step 3: Calculate remaining thickness after future corrosion
    const remainingThickness = thickness - (corrosionRate * assessmentLife);

    // Step 4: Calculate MAWP with current thickness
    // Formula: MAWP = (S * E * t) / (R + 0.6 * t)
    const mawp = (allowableStress * weldEfficiency * thickness) / (radius + 0.6 * thickness);

    // Step 5: Calculate remaining life
    const availableThickness = thickness - futureCorrosionAllowance - treq;
    const remainingLife = Math.max(0, availableThickness / corrosionRate);

    // Step 6: Acceptance criteria
    const acceptable = remainingThickness >= tcmin;
    const safetyFactor = remainingThickness / treq;

    res.json({
      // Input parameters
      thickness,
      corrosionRate,
      designPressure,
      allowableStress,
      insideDiameter,
      radius,
      weldEfficiency,
      futureCorrosionAllowance,
      assessmentLife,

      // Calculated results
      treq: parseFloat(treq.toFixed(4)),
      tcmin: parseFloat(tcmin.toFixed(4)),
      remainingThickness: parseFloat(remainingThickness.toFixed(4)),
      mawp: parseFloat(mawp.toFixed(2)),
      remainingLife: parseFloat(remainingLife.toFixed(1)),
      safetyFactor: parseFloat(safetyFactor.toFixed(3)),

      // Assessment results
      acceptable,
      assessmentLevel: "LEVEL_1",
      calculationMethod: "API 579-1 Part 4 Level 1 - General Metal Loss",
      standard: "ASME Section VIII Div 1",

      // Additional info
      notes: acceptable ?
        "Component is acceptable for continued operation" :
        "Component requires repair or replacement"
    });

  } catch (error) {
    res.status(400).json({
      error: "Calculation error",
      message: error.message
    });
  }
});

app.post('/api/v1/calculations/part5/level1', (req, res) => {
  const {
    nominalThickness,
    localThickness,
    length,
    designPressure,
    allowableStress = 17500, // psi, default for carbon steel
    insideDiameter = 60.0, // inches, default if not provided
    weldEfficiency = 1.0,  // default if not provided
    futureCorrosionAllowance = 0.1, // inches, default if not provided
    circumferentialExtent = 6.0 // inches, default if not provided
  } = req.body;

  try {
    // API 579-1 Part 5 Level 1 - Local Thin Areas calculation
    const radius = insideDiameter / 2.0;

    // Step 1: Calculate required thickness using ASME formula
    // Formula: t_req = (P * R) / (S * E - 0.6 * P)
    const treq = (designPressure * radius) / (allowableStress * weldEfficiency - 0.6 * designPressure);

    // Step 2: Calculate assessment length for screening
    // L_assess = 1.8 * sqrt(R * t_nom)
    const assessmentLength = 1.8 * Math.sqrt(radius * nominalThickness);

    // Step 3: Calculate thickness ratio
    const thicknessRatio = localThickness / nominalThickness;

    // Step 4: Calculate longitudinal and circumferential extents
    const longitudinalExtent = length;
    const circumferentialExtent_calc = circumferentialExtent;

    // Step 5: Calculate Remaining Strength Factor (RSF)
    const effectiveThickness = localThickness - futureCorrosionAllowance;
    const rsf = effectiveThickness / treq;

    // Step 6: Calculate MAWP with LTA
    // Formula: MAWP = (S * E * t_local) / (R + 0.6 * t_local)
    const mawp = (allowableStress * weldEfficiency * localThickness) / (radius + 0.6 * localThickness);

    // Step 7: Level 1 acceptance criteria
    // For Level 1: RSF >= 0.9 and geometric constraints
    const RSF_A = 0.9;
    const geometricCheck = (longitudinalExtent <= assessmentLength) && (thicknessRatio >= 0.2);
    const acceptable = (rsf >= RSF_A) && geometricCheck;

    // Step 8: Calculate critical dimensions
    const criticalLength = assessmentLength;
    const minThicknessRatio = 0.2;

    res.json({
      // Input parameters
      nominalThickness,
      localThickness,
      length,
      designPressure,
      allowableStress,
      insideDiameter,
      radius,
      weldEfficiency,
      futureCorrosionAllowance,
      circumferentialExtent,

      // Calculated results
      treq: parseFloat(treq.toFixed(4)),
      assessmentLength: parseFloat(assessmentLength.toFixed(2)),
      thicknessRatio: parseFloat(thicknessRatio.toFixed(3)),
      longitudinalExtent: parseFloat(longitudinalExtent.toFixed(2)),
      circumferentialExtent: parseFloat(circumferentialExtent_calc.toFixed(2)),
      rsf: parseFloat(rsf.toFixed(3)),
      mawp: parseFloat(mawp.toFixed(2)),

      // Critical values
      criticalLength: parseFloat(criticalLength.toFixed(2)),
      minThicknessRatio: parseFloat(minThicknessRatio.toFixed(1)),
      requiredRSF: RSF_A,

      // Assessment results
      acceptable,
      geometricCheck,
      assessmentLevel: "LEVEL_1",
      calculationMethod: "API 579-1 Part 5 Level 1 - Local Thin Areas",
      standard: "ASME Section VIII Div 1",

      // Additional info
      notes: acceptable ?
        "Local thin area is acceptable for continued operation" :
        "Local thin area requires Level 2 assessment or repair"
    });

  } catch (error) {
    res.status(400).json({
      error: "Calculation error",
      message: error.message
    });
  }
});

app.post('/api/v1/calculations/part6/level1', (req, res) => {
  const {
    pitDepth,
    wallThickness,
    pitDiameter,
    designPressure,
    allowableStress = 17500, // psi, default for carbon steel
    insideDiameter = 60.0, // inches, default if not provided
    weldEfficiency = 1.0,  // default if not provided
    futureCorrosionAllowance = 0.1, // inches, default if not provided
    numberOfPits = 1, // default if not provided
    pitSpacing = 12.0 // inches, default if not provided
  } = req.body;

  try {
    // API 579-1 Part 6 Level 1 - Pitting Corrosion calculation
    const radius = insideDiameter / 2.0;

    // Step 1: Calculate required thickness using ASME formula
    // Formula: t_req = (P * R) / (S * E - 0.6 * P)
    const treq = (designPressure * radius) / (allowableStress * weldEfficiency - 0.6 * designPressure);

    // Step 2: Calculate remaining thickness at pit location
    const remainingThickness = wallThickness - pitDepth;

    // Step 3: Calculate pit depth ratio
    const depthRatio = pitDepth / wallThickness;

    // Step 4: Calculate pit diameter to thickness ratio
    const diameterToThicknessRatio = pitDiameter / wallThickness;

    // Step 5: Calculate pit density (pits per unit area)
    const assessmentArea = Math.PI * Math.pow(insideDiameter/2, 2); // Simplified for demonstration
    const pitDensity = numberOfPits / assessmentArea;

    // Step 6: Calculate RSF for pitting
    // Formula: RSF = (t_remaining - FCA) / t_req
    const effectiveThickness = remainingThickness - futureCorrosionAllowance;
    const rsf = effectiveThickness / treq;

    // Step 7: Calculate MAWP with pits
    // Formula: MAWP = (S * E * t_remaining) / (R + 0.6 * t_remaining)
    const mawp = (allowableStress * weldEfficiency * remainingThickness) / (radius + 0.6 * remainingThickness);

    // Step 8: Level 1 acceptance criteria for pitting
    const RSF_A = 0.9;
    const maxDepthRatio = 0.5; // Maximum allowable depth ratio for Level 1
    const maxDiameterRatio = 2.0; // Maximum diameter to thickness ratio

    // Check all Level 1 criteria
    const depthCheck = depthRatio <= maxDepthRatio;
    const diameterCheck = diameterToThicknessRatio <= maxDiameterRatio;
    const rsfCheck = rsf >= RSF_A;
    const spacingCheck = pitSpacing >= (2 * pitDiameter); // Minimum spacing requirement

    const acceptable = depthCheck && diameterCheck && rsfCheck && spacingCheck;

    // Step 9: Calculate maximum allowable pit depth
    const maxAllowableDepth = wallThickness * maxDepthRatio;

    // Step 10: Calculate critical pit diameter
    const criticalPitDiameter = wallThickness * maxDiameterRatio;

    res.json({
      // Input parameters
      pitDepth,
      wallThickness,
      pitDiameter,
      designPressure,
      allowableStress,
      insideDiameter,
      radius,
      weldEfficiency,
      futureCorrosionAllowance,
      numberOfPits,
      pitSpacing,

      // Calculated results
      treq: parseFloat(treq.toFixed(4)),
      remainingThickness: parseFloat(remainingThickness.toFixed(4)),
      depthRatio: parseFloat(depthRatio.toFixed(3)),
      diameterToThicknessRatio: parseFloat(diameterToThicknessRatio.toFixed(2)),
      pitDensity: parseFloat(pitDensity.toFixed(6)),
      rsf: parseFloat(rsf.toFixed(3)),
      mawp: parseFloat(mawp.toFixed(2)),

      // Critical values
      maxAllowableDepth: parseFloat(maxAllowableDepth.toFixed(3)),
      criticalPitDiameter: parseFloat(criticalPitDiameter.toFixed(2)),
      maxDepthRatio: maxDepthRatio,
      maxDiameterRatio: maxDiameterRatio,
      requiredRSF: RSF_A,

      // Individual checks
      depthCheck,
      diameterCheck,
      rsfCheck,
      spacingCheck,

      // Assessment results
      acceptable,
      assessmentLevel: "LEVEL_1",
      calculationMethod: "API 579-1 Part 6 Level 1 - Pitting Corrosion",
      standard: "ASME Section VIII Div 1",

      // Additional info
      notes: acceptable ?
        "Pitting corrosion is acceptable for continued operation" :
        "Pitting requires Level 2 assessment, repair, or replacement"
    });

  } catch (error) {
    res.status(400).json({
      error: "Calculation error",
      message: error.message
    });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'FFS Mock Backend is running' });
});

app.listen(port, () => {
  console.log(`🚀 FFS Mock Backend running at http://localhost:${port}`);
  console.log(`📊 Dashboard data available at http://localhost:${port}/api/v1/assessments/statistics`);
  console.log(`🔧 Health check at http://localhost:${port}/health`);
});
