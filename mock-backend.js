const express = require('express');
const cors = require('cors');
const app = express();
const port = 8080;

// Enable CORS for all routes
app.use(cors());
app.use(express.json());

// Mock data
const mockAssessments = [
  {
    id: 1,
    equipmentId: "TANK-001",
    assessmentType: "PART_4_GENERAL_METAL_LOSS",
    status: "COMPLETED",
    createdDate: "2024-01-15",
    result: "ACCEPTABLE"
  },
  {
    id: 2,
    equipmentId: "PIPE-002", 
    assessmentType: "PART_5_LOCAL_THIN_AREAS",
    status: "IN_PROGRESS",
    createdDate: "2024-01-16",
    result: "PENDING"
  }
];

const mockMaterials = [
  {
    id: 1,
    name: "SA-516 Grade 70",
    specification: "SA-516",
    grade: "70",
    yieldStrength: 262.0,
    tensileStrength: 485.0,
    allowableStress: 138.0
  },
  {
    id: 2,
    name: "SA-285 Grade C",
    specification: "SA-285", 
    grade: "C",
    yieldStrength: 207.0,
    tensileStrength: 380.0,
    allowableStress: 95.0
  }
];

const mockStatistics = {
  totalAssessments: 25,
  completedAssessments: 18,
  pendingAssessments: 7,
  acceptableResults: 15,
  unacceptableResults: 3
};

// API Routes
app.get('/api/v1/assessments/statistics', (req, res) => {
  res.json(mockStatistics);
});

app.get('/api/v1/assessments', (req, res) => {
  res.json(mockAssessments);
});

app.get('/api/v1/assessments/:id', (req, res) => {
  const assessment = mockAssessments.find(a => a.id == req.params.id);
  if (assessment) {
    res.json(assessment);
  } else {
    res.status(404).json({ error: 'Assessment not found' });
  }
});

app.post('/api/v1/assessments', (req, res) => {
  const newAssessment = {
    id: mockAssessments.length + 1,
    ...req.body,
    status: 'IN_PROGRESS',
    createdDate: new Date().toISOString().split('T')[0]
  };
  mockAssessments.push(newAssessment);
  res.status(201).json(newAssessment);
});

app.get('/api/v1/materials', (req, res) => {
  res.json(mockMaterials);
});

app.get('/api/v1/materials/:id', (req, res) => {
  const material = mockMaterials.find(m => m.id == req.params.id);
  if (material) {
    res.json(material);
  } else {
    res.status(404).json({ error: 'Material not found' });
  }
});

// FFS Calculation endpoints
app.post('/api/v1/calculations/part4/level1', (req, res) => {
  const { thickness, corrosionRate, designPressure, allowableStress } = req.body;
  
  // Mock Part 4 Level 1 calculation
  const remainingThickness = thickness - (corrosionRate * 10); // 10 years
  const requiredThickness = (designPressure * 12) / (2 * allowableStress);
  const acceptable = remainingThickness >= requiredThickness;
  
  res.json({
    remainingThickness,
    requiredThickness,
    acceptable,
    safetyFactor: remainingThickness / requiredThickness,
    calculationMethod: "API 579-1 Part 4 Level 1"
  });
});

app.post('/api/v1/calculations/part5/level1', (req, res) => {
  const { nominalThickness, localThickness, length, designPressure } = req.body;
  
  // Mock Part 5 Level 1 calculation
  const thicknessRatio = localThickness / nominalThickness;
  const acceptable = thicknessRatio >= 0.2 && length <= 100;
  
  res.json({
    thicknessRatio,
    acceptable,
    criticalLength: 100,
    calculationMethod: "API 579-1 Part 5 Level 1"
  });
});

app.post('/api/v1/calculations/part6/level1', (req, res) => {
  const { pitDepth, wallThickness, pitDiameter } = req.body;
  
  // Mock Part 6 Level 1 calculation
  const depthRatio = pitDepth / wallThickness;
  const acceptable = depthRatio <= 0.5 && pitDiameter <= 25;
  
  res.json({
    depthRatio,
    acceptable,
    maxAllowableDepth: wallThickness * 0.5,
    calculationMethod: "API 579-1 Part 6 Level 1"
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'FFS Mock Backend is running' });
});

app.listen(port, () => {
  console.log(`🚀 FFS Mock Backend running at http://localhost:${port}`);
  console.log(`📊 Dashboard data available at http://localhost:${port}/api/v1/assessments/statistics`);
  console.log(`🔧 Health check at http://localhost:${port}/health`);
});
