# Fitness-for-Service (FFS) Assessment Application

A comprehensive cross-platform web application for performing Level 1 and Level 2 Fitness-for-Service assessments of pressure systems and pipelines, fully compliant with **API 579-1 / ASME FFS-1** standards.

## 🎯 Overview

This application provides engineers with a robust platform to perform FFS assessments covering:

- **Part 4**: General Metal Loss
- **Part 5**: Local Thin Areas (LTA) and Groove-like Flaws
- **Part 6**: Pitting Corrosion

## ✨ Key Features

### Assessment Capabilities
- ✅ **Level 1 & Level 2 Assessments** for Parts 4, 5, and 6
- ✅ **Component Classification** (Type A/B, Class 1/2)
- ✅ **Critical Thickness Profile (CTP)** analysis
- ✅ **Remaining Strength Factor (RSF)** calculations
- ✅ **MAWP calculations** with pressure reduction scenarios
- ✅ **Remaining life estimation** based on corrosion rates

### Material Database
- ✅ **Integrated material database** with API/ASME specifications
- ✅ **Temperature-dependent properties** (allowable stress, yield strength, etc.)
- ✅ **Fatigue curve classifications** (A, B, C, D)
- ✅ **Material condition tracking** (normalized, fine-grain practice, etc.)

### Data Management
- ✅ **NDT data import** (CSV, Excel) for thickness and pitting measurements
- ✅ **Automated thickness profile analysis**
- ✅ **Pit-couple analysis** for pitting assessments
- ✅ **Data validation and error checking**

### Reporting & Analysis
- ✅ **PDF report generation** with detailed calculations
- ✅ **What-if scenario simulations**
- ✅ **Assessment history and audit trails**
- ✅ **Statistical dashboards and metrics**

### User Experience
- ✅ **Responsive web interface** (desktop, tablet, mobile)
- ✅ **Secure user authentication**
- ✅ **Multi-user environment support**
- ✅ **Real-time calculation feedback**

## 🏗️ Architecture

### Backend (Spring Boot)
- **Framework**: Spring Boot 3.2.0 with Java 17
- **Database**: PostgreSQL with JPA/Hibernate
- **Security**: Spring Security with JWT authentication
- **API**: RESTful services with comprehensive validation
- **Testing**: JUnit 5 with comprehensive test coverage

### Frontend (React)
- **Framework**: React 18 with modern hooks
- **UI Library**: React Bootstrap for responsive design
- **State Management**: React Context and hooks
- **Charts**: Chart.js for data visualization
- **Forms**: Formik with Yup validation

### Calculation Engine
- **Standards Compliance**: API 579-1 / ASME FFS-1
- **Precision**: Configurable decimal precision
- **Validation**: Comprehensive input validation
- **Error Handling**: Detailed error reporting and recovery

## 🚀 Quick Start

### Prerequisites
- Java 17 or higher
- Node.js 18 or higher
- PostgreSQL 12 or higher
- Maven 3.8 or higher

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/ffs-assessment.git
   cd ffs-assessment
   ```

2. **Setup Database**
   ```sql
   CREATE DATABASE ffs_db;
   CREATE USER ffs_user WITH PASSWORD 'ffs_password';
   GRANT ALL PRIVILEGES ON DATABASE ffs_db TO ffs_user;
   ```

3. **Configure Application**
   ```bash
   # Copy and edit application configuration
   cp src/main/resources/application.yml.example src/main/resources/application.yml
   # Update database connection details
   ```

4. **Build and Run Backend**
   ```bash
   # Install dependencies and run tests
   mvn clean install
   
   # Run the application
   mvn spring-boot:run
   ```

5. **Build and Run Frontend**
   ```bash
   cd src/main/frontend
   npm install
   npm start
   ```

6. **Access Application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8080/api
   - API Documentation: http://localhost:8080/swagger-ui.html

## 📊 Usage Examples

### Part 4 Assessment Example

Based on the provided API 579-1 examples:

```json
{
  "name": "Horizontal Vessel - General Metal Loss",
  "assessmentType": "PART_4_GENERAL_METAL_LOSS",
  "assessmentLevel": "LEVEL_1",
  "componentType": "Cylindrical Vessel",
  "designPressure": 653.0,
  "designTemperature": 200.0,
  "insideDiameter": 19.055,
  "nominalThickness": 0.630,
  "materialId": 1,
  "futureCorrosionAllowance": 0.098,
  "corrosionRate": 0.004,
  "thicknessReadings": [
    {"sequenceNumber": 1, "thickness": 0.512},
    {"sequenceNumber": 2, "thickness": 0.472},
    {"sequenceNumber": 3, "thickness": 0.433}
  ]
}
```

### Expected Results
- **tmm**: 0.433 in (minimum measured thickness)
- **tam**: 0.472 in (average measured thickness)
- **Level 1**: Not acceptable (requires Level 2 or repair)
- **Level 2**: Acceptable with RSF = 0.766
- **Remaining Life**: 33.6 years

## 🧪 Testing

### Run All Tests
```bash
mvn test
```

### Run Specific Test Categories
```bash
# Unit tests only
mvn test -Dtest="*Test"

# Integration tests only
mvn test -Dtest="*IT"

# Calculation engine tests
mvn test -Dtest="*CalculationServiceTest"
```

### Frontend Tests
```bash
cd src/main/frontend
npm test
```

## 📚 API Documentation

### Assessment Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/v1/assessments` | Get all assessments |
| POST | `/api/v1/assessments` | Create new assessment |
| GET | `/api/v1/assessments/{id}` | Get assessment by ID |
| PUT | `/api/v1/assessments/{id}` | Update assessment |
| DELETE | `/api/v1/assessments/{id}` | Delete assessment |
| POST | `/api/v1/assessments/{id}/part4/level1` | Execute Part 4 Level 1 |
| POST | `/api/v1/assessments/{id}/part4/level2` | Execute Part 4 Level 2 |

### Material Database Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/v1/materials` | Get all materials |
| POST | `/api/v1/materials` | Add new material |
| GET | `/api/v1/materials/{id}` | Get material by ID |
| GET | `/api/v1/materials/search` | Search materials |

## 🔧 Configuration

### Application Properties

```yaml
# Database Configuration
spring:
  datasource:
    url: ***************************************
    username: ffs_user
    password: ffs_password

# FFS Specific Configuration
ffs:
  calculation:
    precision: 6
    rounding-mode: HALF_UP
  materials:
    auto-load: true
  reports:
    output-directory: ${user.home}/ffs-reports
```

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection URL | localhost:5432/ffs_db |
| `JWT_SECRET` | JWT signing secret | (generated) |
| `LOG_LEVEL` | Application log level | INFO |

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow Java coding standards (Google Style Guide)
- Write comprehensive tests for new features
- Update documentation for API changes
- Ensure all tests pass before submitting PR

## 📋 Standards Compliance

This application implements calculations according to:

- **API 579-1**: Fitness-For-Service Standard
- **ASME FFS-1**: Fitness-For-Service Standard
- **ASME Section VIII**: Pressure Vessel Code
- **ASME Section II**: Materials Specifications

### Validation Examples

The application has been validated against published examples from:
- API 579-1 Part 4 Examples 1-2
- API 579-1 Part 5 Examples 1-3  
- API 579-1 Part 6 Examples 1-3

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Wiki](https://github.com/your-org/ffs-assessment/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-org/ffs-assessment/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/ffs-assessment/discussions)

## 🏆 Acknowledgments

- API 579-1 / ASME FFS-1 Standards Committee
- Spring Boot and React communities
- Contributors and beta testers

---

**⚠️ Important Notice**: This software is provided for engineering analysis purposes. Users are responsible for verifying results and ensuring compliance with applicable codes and standards. Always consult with qualified engineers for critical assessments.
