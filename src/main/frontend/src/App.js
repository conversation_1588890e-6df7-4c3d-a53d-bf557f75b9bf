import React from 'react';
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import Navigation from './components/Navigation';
import Dashboard from './components/Dashboard';
import AssessmentList from './components/AssessmentList';
import AssessmentForm from './components/AssessmentForm';
import AssessmentDetail from './components/AssessmentDetail';
import MaterialDatabase from './components/MaterialDatabase';
import Reports from './components/Reports';
import './App.css';

/**
 * Main Application Component
 * Provides routing and layout for the FFS Assessment Application
 */
function App() {
  return (
    <Router>
      <div className="App">
        <Navigation />
        <main className="container-fluid mt-3">
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/assessments" element={<AssessmentList />} />
            <Route path="/assessments/new" element={<AssessmentForm />} />
            <Route path="/assessments/:id" element={<AssessmentDetail />} />
            <Route path="/assessments/:id/edit" element={<AssessmentForm />} />
            <Route path="/materials" element={<MaterialDatabase />} />
            <Route path="/reports" element={<Reports />} />
          </Routes>
        </main>
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
        />
      </div>
    </Router>
  );
}

export default App;
