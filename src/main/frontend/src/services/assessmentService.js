import axios from 'axios';

/**
 * Service for FFS Assessment API calls
 */
class AssessmentService {
  constructor() {
    this.baseURL = '/api/v1/assessments';
    
    // Configure axios defaults
    axios.defaults.headers.common['Content-Type'] = 'application/json';
    
    // Add request interceptor for error handling
    axios.interceptors.response.use(
      response => response,
      error => {
        console.error('API Error:', error);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Get all assessments
   */
  async getAllAssessments() {
    return axios.get(this.baseURL);
  }

  /**
   * Get assessment by ID
   */
  async getAssessmentById(id) {
    return axios.get(`${this.baseURL}/${id}`);
  }

  /**
   * Create new assessment
   */
  async createAssessment(assessmentData) {
    return axios.post(this.baseURL, assessmentData);
  }

  /**
   * Update existing assessment
   */
  async updateAssessment(id, assessmentData) {
    return axios.put(`${this.baseURL}/${id}`, assessmentData);
  }

  /**
   * Delete assessment
   */
  async deleteAssessment(id) {
    return axios.delete(`${this.baseURL}/${id}`);
  }

  /**
   * Execute Part 4 Level 1 Assessment
   */
  async executePart4Level1(id) {
    return axios.post(`${this.baseURL}/${id}/part4/level1`);
  }

  /**
   * Execute Part 4 Level 2 Assessment
   */
  async executePart4Level2(id) {
    return axios.post(`${this.baseURL}/${id}/part4/level2`);
  }

  /**
   * Execute Part 5 Level 1 Assessment
   */
  async executePart5Level1(id) {
    return axios.post(`${this.baseURL}/${id}/part5/level1`);
  }

  /**
   * Execute Part 5 Level 2 Assessment
   */
  async executePart5Level2(id) {
    return axios.post(`${this.baseURL}/${id}/part5/level2`);
  }

  /**
   * Execute Part 6 Level 1 Assessment
   */
  async executePart6Level1(id) {
    return axios.post(`${this.baseURL}/${id}/part6/level1`);
  }

  /**
   * Execute Part 6 Level 2 Assessment
   */
  async executePart6Level2(id) {
    return axios.post(`${this.baseURL}/${id}/part6/level2`);
  }

  /**
   * Get assessment statistics
   */
  async getStatistics() {
    return axios.get(`${this.baseURL}/statistics`);
  }

  /**
   * Search assessments
   */
  async searchAssessments(criteria) {
    const params = new URLSearchParams();
    
    if (criteria.componentType) {
      params.append('componentType', criteria.componentType);
    }
    if (criteria.assessmentType) {
      params.append('assessmentType', criteria.assessmentType);
    }
    if (criteria.status) {
      params.append('status', criteria.status);
    }
    
    return axios.get(`${this.baseURL}/search?${params.toString()}`);
  }

  /**
   * Upload thickness data from file
   */
  async uploadThicknessData(assessmentId, file) {
    const formData = new FormData();
    formData.append('file', file);
    
    return axios.post(`${this.baseURL}/${assessmentId}/thickness-data`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  /**
   * Upload pitting data from file
   */
  async uploadPittingData(assessmentId, file) {
    const formData = new FormData();
    formData.append('file', file);
    
    return axios.post(`${this.baseURL}/${assessmentId}/pitting-data`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  /**
   * Generate assessment report
   */
  async generateReport(assessmentId, format = 'pdf') {
    return axios.get(`${this.baseURL}/${assessmentId}/report`, {
      params: { format },
      responseType: 'blob'
    });
  }

  /**
   * Export assessment data
   */
  async exportAssessment(assessmentId, format = 'json') {
    return axios.get(`${this.baseURL}/${assessmentId}/export`, {
      params: { format },
      responseType: format === 'json' ? 'json' : 'blob'
    });
  }

  /**
   * Get assessment validation errors
   */
  async validateAssessment(assessmentData) {
    return axios.post(`${this.baseURL}/validate`, assessmentData);
  }

  /**
   * Perform what-if analysis
   */
  async performWhatIfAnalysis(assessmentId, scenarios) {
    return axios.post(`${this.baseURL}/${assessmentId}/what-if`, scenarios);
  }

  /**
   * Get assessment history/audit trail
   */
  async getAssessmentHistory(assessmentId) {
    return axios.get(`${this.baseURL}/${assessmentId}/history`);
  }

  /**
   * Clone/duplicate an assessment
   */
  async cloneAssessment(assessmentId, newName) {
    return axios.post(`${this.baseURL}/${assessmentId}/clone`, { name: newName });
  }

  /**
   * Get assessment templates
   */
  async getAssessmentTemplates() {
    return axios.get(`${this.baseURL}/templates`);
  }

  /**
   * Create assessment from template
   */
  async createFromTemplate(templateId, assessmentData) {
    return axios.post(`${this.baseURL}/templates/${templateId}`, assessmentData);
  }
}

// Create and export a singleton instance
const assessmentService = new AssessmentService();
export default assessmentService;
