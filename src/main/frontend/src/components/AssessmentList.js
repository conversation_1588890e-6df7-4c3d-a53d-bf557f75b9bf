import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Badge, Alert } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import assessmentService from '../services/assessmentService';

/**
 * Assessment List component showing all assessments
 */
const AssessmentList = () => {
  const [assessments, setAssessments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadAssessments();
  }, []);

  const loadAssessments = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await assessmentService.getAllAssessments();
      setAssessments(response.data);
    } catch (err) {
      console.error('Error loading assessments:', err);
      setError('Failed to load assessments');
      toast.error('Failed to load assessments');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadgeVariant = (status) => {
    switch (status) {
      case 'DRAFT': return 'secondary';
      case 'IN_PROGRESS': return 'warning';
      case 'COMPLETED': return 'success';
      case 'REVIEWED': return 'info';
      case 'APPROVED': return 'primary';
      default: return 'secondary';
    }
  };

  const getAcceptabilityBadge = (isAcceptable) => {
    if (isAcceptable === null || isAcceptable === undefined) return null;
    return (
      <Badge bg={isAcceptable ? 'success' : 'danger'}>
        {isAcceptable ? 'Acceptable' : 'Not Acceptable'}
      </Badge>
    );
  };

  if (loading) {
    return (
      <Container>
        <div className="loading-spinner">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <Alert variant="danger">
          <Alert.Heading>Error</Alert.Heading>
          <p>{error}</p>
          <Button variant="outline-danger" onClick={loadAssessments}>
            Try Again
          </Button>
        </Alert>
      </Container>
    );
  }

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h1 className="h3 mb-0">FFS Assessments</h1>
          <p className="text-muted">Manage and review fitness-for-service assessments</p>
        </Col>
        <Col xs="auto">
          <Link to="/assessments/new">
            <Button variant="primary">
              <i className="fas fa-plus me-2"></i>
              New Assessment
            </Button>
          </Link>
        </Col>
      </Row>

      <Card>
        <Card.Header>
          <h5 className="mb-0">All Assessments ({assessments.length})</h5>
        </Card.Header>
        <Card.Body className="p-0">
          {assessments.length === 0 ? (
            <div className="text-center py-5">
              <i className="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
              <h5 className="text-muted">No assessments found</h5>
              <p className="text-muted">Create your first FFS assessment to get started</p>
              <Link to="/assessments/new">
                <Button variant="primary">
                  <i className="fas fa-plus me-2"></i>
                  Create Assessment
                </Button>
              </Link>
            </div>
          ) : (
            <Table responsive hover className="mb-0">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Type</th>
                  <th>Component</th>
                  <th>Status</th>
                  <th>Result</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {assessments.map(assessment => (
                  <tr key={assessment.id}>
                    <td>
                      <Link 
                        to={`/assessments/${assessment.id}`} 
                        className="text-decoration-none fw-bold"
                      >
                        {assessment.name}
                      </Link>
                      {assessment.description && (
                        <div className="small text-muted">{assessment.description}</div>
                      )}
                    </td>
                    <td>
                      <Badge bg="info" className="small">
                        {assessment.assessmentTypeDescription}
                      </Badge>
                    </td>
                    <td>
                      <div>{assessment.componentType}</div>
                      {assessment.componentId && (
                        <div className="small text-muted">ID: {assessment.componentId}</div>
                      )}
                    </td>
                    <td>
                      <Badge bg={getStatusBadgeVariant(assessment.status)}>
                        {assessment.status}
                      </Badge>
                    </td>
                    <td>
                      {getAcceptabilityBadge(assessment.isAcceptable)}
                      {assessment.mawp && (
                        <div className="small text-muted">
                          MAWP: {assessment.mawp.toFixed(1)} psi
                        </div>
                      )}
                    </td>
                    <td>
                      <div>{new Date(assessment.createdAt).toLocaleDateString()}</div>
                      <div className="small text-muted">
                        {assessment.createdBy || 'System'}
                      </div>
                    </td>
                    <td>
                      <div className="btn-group btn-group-sm">
                        <Link 
                          to={`/assessments/${assessment.id}`}
                          className="btn btn-outline-primary"
                          title="View Details"
                        >
                          <i className="fas fa-eye"></i>
                        </Link>
                        <Link 
                          to={`/assessments/${assessment.id}/edit`}
                          className="btn btn-outline-secondary"
                          title="Edit"
                        >
                          <i className="fas fa-edit"></i>
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
};

export default AssessmentList;
