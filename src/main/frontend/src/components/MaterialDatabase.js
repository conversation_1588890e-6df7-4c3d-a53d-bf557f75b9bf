import React, { useState, useEffect } from 'react';
import { Container, Card, Table, Button, Modal, Form, Row, Col, Alert, Badge } from 'react-bootstrap';
import axios from 'axios';

const MaterialDatabase = () => {
  const [materials, setMaterials] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState(null);

  useEffect(() => {
    fetchMaterials();
  }, []);

  const fetchMaterials = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/v1/materials');
      setMaterials(response.data);
    } catch (error) {
      setError('Failed to load materials');
      console.error('Error fetching materials:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = (material) => {
    setSelectedMaterial(material);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedMaterial(null);
  };

  const getSpecificationBadge = (spec) => {
    const badgeColors = {
      'SA-516': 'primary',
      'SA-285': 'success',
      'SA-515': 'warning',
      'SA-537': 'info'
    };
    return badgeColors[spec] || 'secondary';
  };

  if (loading) {
    return (
      <Container>
        <Card>
          <Card.Body>
            <div className="text-center">Loading materials...</div>
          </Card.Body>
        </Card>
      </Container>
    );
  }

  return (
    <Container>
      <Card>
        <Card.Header className="d-flex justify-content-between align-items-center">
          <h5 className="mb-0">Material Database</h5>
          <Badge bg="info">{materials.length} Materials</Badge>
        </Card.Header>
        <Card.Body>
          {error && <Alert variant="danger">{error}</Alert>}

          <div className="mb-3">
            <p className="text-muted">
              Standard materials database for FFS assessments according to API 579-1.
              Click on any material to view detailed properties.
            </p>
          </div>

          <Table striped bordered hover responsive>
            <thead>
              <tr>
                <th>Material Name</th>
                <th>Specification</th>
                <th>Grade</th>
                <th>Yield Strength (MPa)</th>
                <th>Tensile Strength (MPa)</th>
                <th>Allowable Stress (MPa)</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {materials.map(material => (
                <tr key={material.id}>
                  <td>
                    <strong>{material.name}</strong>
                  </td>
                  <td>
                    <Badge bg={getSpecificationBadge(material.specification)}>
                      {material.specification}
                    </Badge>
                  </td>
                  <td>{material.grade}</td>
                  <td>{material.yieldStrength}</td>
                  <td>{material.tensileStrength}</td>
                  <td>{material.allowableStress}</td>
                  <td>
                    <Button
                      variant="outline-primary"
                      size="sm"
                      onClick={() => handleViewDetails(material)}
                    >
                      View Details
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>

          {materials.length === 0 && !loading && (
            <div className="text-center text-muted py-4">
              No materials found in the database.
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Material Details Modal */}
      <Modal show={showModal} onHide={handleCloseModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            Material Details - {selectedMaterial?.name}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedMaterial && (
            <Row>
              <Col md={6}>
                <Card>
                  <Card.Header>
                    <h6>Basic Properties</h6>
                  </Card.Header>
                  <Card.Body>
                    <Table borderless>
                      <tbody>
                        <tr>
                          <td><strong>Material Name:</strong></td>
                          <td>{selectedMaterial.name}</td>
                        </tr>
                        <tr>
                          <td><strong>Specification:</strong></td>
                          <td>
                            <Badge bg={getSpecificationBadge(selectedMaterial.specification)}>
                              {selectedMaterial.specification}
                            </Badge>
                          </td>
                        </tr>
                        <tr>
                          <td><strong>Grade:</strong></td>
                          <td>{selectedMaterial.grade}</td>
                        </tr>
                      </tbody>
                    </Table>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={6}>
                <Card>
                  <Card.Header>
                    <h6>Mechanical Properties</h6>
                  </Card.Header>
                  <Card.Body>
                    <Table borderless>
                      <tbody>
                        <tr>
                          <td><strong>Yield Strength:</strong></td>
                          <td>{selectedMaterial.yieldStrength} MPa</td>
                        </tr>
                        <tr>
                          <td><strong>Tensile Strength:</strong></td>
                          <td>{selectedMaterial.tensileStrength} MPa</td>
                        </tr>
                        <tr>
                          <td><strong>Allowable Stress:</strong></td>
                          <td>{selectedMaterial.allowableStress} MPa</td>
                        </tr>
                      </tbody>
                    </Table>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseModal}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default MaterialDatabase;
