import React, { useState, useEffect } from 'react';
import { Contain<PERSON>, Card, Form, Button, Row, Col, Alert, Tabs, Tab, Table } from 'react-bootstrap';
import axios from 'axios';

const AssessmentForm = () => {
  const [formData, setFormData] = useState({
    equipmentId: '',
    assessmentType: 'PART_4_GENERAL_METAL_LOSS',
    materialId: '',
    // Part 4 fields
    thickness: '',
    corrosionRate: '',
    designPressure: '',
    allowableStress: '',
    // Part 5 fields
    nominalThickness: '',
    localThickness: '',
    length: '',
    // Part 6 fields
    pitDepth: '',
    wallThickness: '',
    pitDiameter: ''
  });

  const [materials, setMaterials] = useState([]);
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchMaterials();
  }, []);

  const fetchMaterials = async () => {
    try {
      const response = await axios.get('/api/v1/materials');
      setMaterials(response.data);
    } catch (error) {
      console.error('Error fetching materials:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setResult(null);

    try {
      // First create the assessment
      const assessmentData = {
        equipmentId: formData.equipmentId,
        assessmentType: formData.assessmentType,
        materialId: formData.materialId
      };

      const assessmentResponse = await axios.post('/api/v1/assessments', assessmentData);

      // Then perform the calculation based on type
      let calculationEndpoint = '';
      let calculationData = {};

      switch (formData.assessmentType) {
        case 'PART_4_GENERAL_METAL_LOSS':
          calculationEndpoint = '/api/v1/calculations/part4/level1';
          calculationData = {
            thickness: parseFloat(formData.thickness),
            corrosionRate: parseFloat(formData.corrosionRate),
            designPressure: parseFloat(formData.designPressure),
            allowableStress: parseFloat(formData.allowableStress)
          };
          break;
        case 'PART_5_LOCAL_THIN_AREAS':
          calculationEndpoint = '/api/v1/calculations/part5/level1';
          calculationData = {
            nominalThickness: parseFloat(formData.nominalThickness),
            localThickness: parseFloat(formData.localThickness),
            length: parseFloat(formData.length),
            designPressure: parseFloat(formData.designPressure)
          };
          break;
        case 'PART_6_PITTING_CORROSION':
          calculationEndpoint = '/api/v1/calculations/part6/level1';
          calculationData = {
            pitDepth: parseFloat(formData.pitDepth),
            wallThickness: parseFloat(formData.wallThickness),
            pitDiameter: parseFloat(formData.pitDiameter)
          };
          break;
        default:
          throw new Error('Invalid assessment type');
      }

      const calculationResponse = await axios.post(calculationEndpoint, calculationData);
      setResult({
        assessment: assessmentResponse.data,
        calculation: calculationResponse.data
      });

    } catch (error) {
      setError(error.response?.data?.message || 'Error creating assessment');
    } finally {
      setLoading(false);
    }
  };

  const renderPart4Form = () => (
    <Row>
      <Col md={6}>
        <Form.Group className="mb-3">
          <Form.Label>Current Thickness (mm)</Form.Label>
          <Form.Control
            type="number"
            step="0.1"
            name="thickness"
            value={formData.thickness}
            onChange={handleInputChange}
            required
          />
        </Form.Group>
      </Col>
      <Col md={6}>
        <Form.Group className="mb-3">
          <Form.Label>Corrosion Rate (mm/year)</Form.Label>
          <Form.Control
            type="number"
            step="0.01"
            name="corrosionRate"
            value={formData.corrosionRate}
            onChange={handleInputChange}
            required
          />
        </Form.Group>
      </Col>
      <Col md={6}>
        <Form.Group className="mb-3">
          <Form.Label>Design Pressure (MPa)</Form.Label>
          <Form.Control
            type="number"
            step="0.1"
            name="designPressure"
            value={formData.designPressure}
            onChange={handleInputChange}
            required
          />
        </Form.Group>
      </Col>
      <Col md={6}>
        <Form.Group className="mb-3">
          <Form.Label>Allowable Stress (MPa)</Form.Label>
          <Form.Control
            type="number"
            step="0.1"
            name="allowableStress"
            value={formData.allowableStress}
            onChange={handleInputChange}
            required
          />
        </Form.Group>
      </Col>
    </Row>
  );

  const renderPart5Form = () => (
    <Row>
      <Col md={6}>
        <Form.Group className="mb-3">
          <Form.Label>Nominal Thickness (mm)</Form.Label>
          <Form.Control
            type="number"
            step="0.1"
            name="nominalThickness"
            value={formData.nominalThickness}
            onChange={handleInputChange}
            required
          />
        </Form.Group>
      </Col>
      <Col md={6}>
        <Form.Group className="mb-3">
          <Form.Label>Local Thickness (mm)</Form.Label>
          <Form.Control
            type="number"
            step="0.1"
            name="localThickness"
            value={formData.localThickness}
            onChange={handleInputChange}
            required
          />
        </Form.Group>
      </Col>
      <Col md={6}>
        <Form.Group className="mb-3">
          <Form.Label>Length (mm)</Form.Label>
          <Form.Control
            type="number"
            step="0.1"
            name="length"
            value={formData.length}
            onChange={handleInputChange}
            required
          />
        </Form.Group>
      </Col>
      <Col md={6}>
        <Form.Group className="mb-3">
          <Form.Label>Design Pressure (MPa)</Form.Label>
          <Form.Control
            type="number"
            step="0.1"
            name="designPressure"
            value={formData.designPressure}
            onChange={handleInputChange}
            required
          />
        </Form.Group>
      </Col>
    </Row>
  );

  const renderPart6Form = () => (
    <Row>
      <Col md={6}>
        <Form.Group className="mb-3">
          <Form.Label>Pit Depth (mm)</Form.Label>
          <Form.Control
            type="number"
            step="0.1"
            name="pitDepth"
            value={formData.pitDepth}
            onChange={handleInputChange}
            required
          />
        </Form.Group>
      </Col>
      <Col md={6}>
        <Form.Group className="mb-3">
          <Form.Label>Wall Thickness (mm)</Form.Label>
          <Form.Control
            type="number"
            step="0.1"
            name="wallThickness"
            value={formData.wallThickness}
            onChange={handleInputChange}
            required
          />
        </Form.Group>
      </Col>
      <Col md={6}>
        <Form.Group className="mb-3">
          <Form.Label>Pit Diameter (mm)</Form.Label>
          <Form.Control
            type="number"
            step="0.1"
            name="pitDiameter"
            value={formData.pitDiameter}
            onChange={handleInputChange}
            required
          />
        </Form.Group>
      </Col>
    </Row>
  );

  const renderResults = () => {
    if (!result) return null;

    const { calculation } = result;

    return (
      <Card className="mt-4">
        <Card.Header>
          <h5>Assessment Results</h5>
        </Card.Header>
        <Card.Body>
          <Alert variant={calculation.acceptable ? 'success' : 'danger'}>
            <strong>Result: {calculation.acceptable ? 'ACCEPTABLE' : 'NOT ACCEPTABLE'}</strong>
          </Alert>

          <Table striped bordered hover>
            <tbody>
              {calculation.remainingThickness && (
                <tr>
                  <td><strong>Remaining Thickness</strong></td>
                  <td>{calculation.remainingThickness.toFixed(2)} mm</td>
                </tr>
              )}
              {calculation.requiredThickness && (
                <tr>
                  <td><strong>Required Thickness</strong></td>
                  <td>{calculation.requiredThickness.toFixed(2)} mm</td>
                </tr>
              )}
              {calculation.safetyFactor && (
                <tr>
                  <td><strong>Safety Factor</strong></td>
                  <td>{calculation.safetyFactor.toFixed(2)}</td>
                </tr>
              )}
              {calculation.thicknessRatio && (
                <tr>
                  <td><strong>Thickness Ratio</strong></td>
                  <td>{calculation.thicknessRatio.toFixed(3)}</td>
                </tr>
              )}
              {calculation.depthRatio && (
                <tr>
                  <td><strong>Depth Ratio</strong></td>
                  <td>{calculation.depthRatio.toFixed(3)}</td>
                </tr>
              )}
              <tr>
                <td><strong>Calculation Method</strong></td>
                <td>{calculation.calculationMethod}</td>
              </tr>
            </tbody>
          </Table>
        </Card.Body>
      </Card>
    );
  };

  return (
    <Container>
      <Card>
        <Card.Header>
          <h5>Create New FFS Assessment</h5>
        </Card.Header>
        <Card.Body>
          {error && <Alert variant="danger">{error}</Alert>}

          <Form onSubmit={handleSubmit}>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Equipment ID</Form.Label>
                  <Form.Control
                    type="text"
                    name="equipmentId"
                    value={formData.equipmentId}
                    onChange={handleInputChange}
                    required
                    placeholder="e.g., TANK-001"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Assessment Type</Form.Label>
                  <Form.Select
                    name="assessmentType"
                    value={formData.assessmentType}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="PART_4_GENERAL_METAL_LOSS">Part 4 - General Metal Loss</option>
                    <option value="PART_5_LOCAL_THIN_AREAS">Part 5 - Local Thin Areas</option>
                    <option value="PART_6_PITTING_CORROSION">Part 6 - Pitting Corrosion</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Material</Form.Label>
                  <Form.Select
                    name="materialId"
                    value={formData.materialId}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select Material</option>
                    {materials.map(material => (
                      <option key={material.id} value={material.id}>
                        {material.name} - {material.specification}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <hr />
            <h6>Assessment Parameters</h6>

            {formData.assessmentType === 'PART_4_GENERAL_METAL_LOSS' && renderPart4Form()}
            {formData.assessmentType === 'PART_5_LOCAL_THIN_AREAS' && renderPart5Form()}
            {formData.assessmentType === 'PART_6_PITTING_CORROSION' && renderPart6Form()}

            <div className="d-grid gap-2 mt-4">
              <Button
                variant="primary"
                type="submit"
                disabled={loading}
                size="lg"
              >
                {loading ? 'Calculating...' : 'Perform Assessment'}
              </Button>
            </div>
          </Form>

          {renderResults()}
        </Card.Body>
      </Card>
    </Container>
  );
};

export default AssessmentForm;
