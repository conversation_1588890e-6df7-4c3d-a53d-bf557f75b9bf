import React, { useState, useEffect } from 'react';
import { Contain<PERSON>, Card, Form, Button, Row, Col, Alert, Tabs, Tab, Table } from 'react-bootstrap';
import axios from 'axios';

const AssessmentForm = () => {
  const [formData, setFormData] = useState({
    equipmentId: '',
    assessmentType: 'PART_4_GENERAL_METAL_LOSS',
    materialId: '',
    // Common fields
    insideDiameter: '60.0',
    weldEfficiency: '1.0',
    futureCorrosionAllowance: '0.1',
    // Part 4 fields
    thickness: '',
    corrosionRate: '',
    designPressure: '',
    allowableStress: '',
    assessmentLife: '10',
    // Part 5 fields
    nominalThickness: '',
    localThickness: '',
    length: '',
    circumferentialExtent: '6.0',
    // Part 6 fields
    pitDepth: '',
    wallThickness: '',
    pitDiameter: '',
    numberOfPits: '1',
    pitSpacing: '12.0'
  });

  const [materials, setMaterials] = useState([]);
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchMaterials();
  }, []);

  const fetchMaterials = async () => {
    try {
      const response = await axios.get('/api/v1/materials');
      setMaterials(response.data);
    } catch (error) {
      console.error('Error fetching materials:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setResult(null);

    try {
      // First create the assessment
      const assessmentData = {
        equipmentId: formData.equipmentId,
        assessmentType: formData.assessmentType,
        materialId: formData.materialId
      };

      const assessmentResponse = await axios.post('/api/v1/assessments', assessmentData);

      // Then perform the calculation based on type
      let calculationEndpoint = '';
      let calculationData = {};

      switch (formData.assessmentType) {
        case 'PART_4_GENERAL_METAL_LOSS':
          calculationEndpoint = '/api/v1/calculations/part4/level1';
          calculationData = {
            thickness: parseFloat(formData.thickness),
            corrosionRate: parseFloat(formData.corrosionRate),
            designPressure: parseFloat(formData.designPressure),
            allowableStress: parseFloat(formData.allowableStress),
            insideDiameter: parseFloat(formData.insideDiameter),
            weldEfficiency: parseFloat(formData.weldEfficiency),
            futureCorrosionAllowance: parseFloat(formData.futureCorrosionAllowance),
            assessmentLife: parseFloat(formData.assessmentLife)
          };
          break;
        case 'PART_5_LOCAL_THIN_AREAS':
          calculationEndpoint = '/api/v1/calculations/part5/level1';
          calculationData = {
            nominalThickness: parseFloat(formData.nominalThickness),
            localThickness: parseFloat(formData.localThickness),
            length: parseFloat(formData.length),
            designPressure: parseFloat(formData.designPressure),
            allowableStress: parseFloat(formData.allowableStress),
            insideDiameter: parseFloat(formData.insideDiameter),
            weldEfficiency: parseFloat(formData.weldEfficiency),
            futureCorrosionAllowance: parseFloat(formData.futureCorrosionAllowance),
            circumferentialExtent: parseFloat(formData.circumferentialExtent)
          };
          break;
        case 'PART_6_PITTING_CORROSION':
          calculationEndpoint = '/api/v1/calculations/part6/level1';
          calculationData = {
            pitDepth: parseFloat(formData.pitDepth),
            wallThickness: parseFloat(formData.wallThickness),
            pitDiameter: parseFloat(formData.pitDiameter),
            designPressure: parseFloat(formData.designPressure),
            allowableStress: parseFloat(formData.allowableStress),
            insideDiameter: parseFloat(formData.insideDiameter),
            weldEfficiency: parseFloat(formData.weldEfficiency),
            futureCorrosionAllowance: parseFloat(formData.futureCorrosionAllowance),
            numberOfPits: parseFloat(formData.numberOfPits),
            pitSpacing: parseFloat(formData.pitSpacing)
          };
          break;
        default:
          throw new Error('Invalid assessment type');
      }

      const calculationResponse = await axios.post(calculationEndpoint, calculationData);
      setResult({
        assessment: assessmentResponse.data,
        calculation: calculationResponse.data
      });

    } catch (error) {
      setError(error.response?.data?.message || 'Error creating assessment');
    } finally {
      setLoading(false);
    }
  };

  const renderPart4Form = () => (
    <>
      <h6 className="text-primary mb-3">Vessel Geometry</h6>
      <Row>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Inside Diameter (inches)</Form.Label>
            <Form.Control
              type="number"
              step="0.1"
              name="insideDiameter"
              value={formData.insideDiameter}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Current Thickness (inches)</Form.Label>
            <Form.Control
              type="number"
              step="0.001"
              name="thickness"
              value={formData.thickness}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
      </Row>

      <h6 className="text-primary mb-3">Design Parameters</h6>
      <Row>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Design Pressure (psi)</Form.Label>
            <Form.Control
              type="number"
              step="0.1"
              name="designPressure"
              value={formData.designPressure}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Allowable Stress (psi)</Form.Label>
            <Form.Control
              type="number"
              step="1"
              name="allowableStress"
              value={formData.allowableStress}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Weld Efficiency</Form.Label>
            <Form.Control
              type="number"
              step="0.01"
              min="0.1"
              max="1.0"
              name="weldEfficiency"
              value={formData.weldEfficiency}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
      </Row>

      <h6 className="text-primary mb-3">Corrosion Assessment</h6>
      <Row>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Corrosion Rate (inches/year)</Form.Label>
            <Form.Control
              type="number"
              step="0.001"
              name="corrosionRate"
              value={formData.corrosionRate}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Future Corrosion Allowance (inches)</Form.Label>
            <Form.Control
              type="number"
              step="0.01"
              name="futureCorrosionAllowance"
              value={formData.futureCorrosionAllowance}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Assessment Life (years)</Form.Label>
            <Form.Control
              type="number"
              step="1"
              name="assessmentLife"
              value={formData.assessmentLife}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
      </Row>
    </>
  );

  const renderPart5Form = () => (
    <>
      <h6 className="text-primary mb-3">Vessel Geometry</h6>
      <Row>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Inside Diameter (inches)</Form.Label>
            <Form.Control
              type="number"
              step="0.1"
              name="insideDiameter"
              value={formData.insideDiameter}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Nominal Thickness (inches)</Form.Label>
            <Form.Control
              type="number"
              step="0.001"
              name="nominalThickness"
              value={formData.nominalThickness}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
      </Row>

      <h6 className="text-primary mb-3">Local Thin Area Dimensions</h6>
      <Row>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Local Thickness (inches)</Form.Label>
            <Form.Control
              type="number"
              step="0.001"
              name="localThickness"
              value={formData.localThickness}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Longitudinal Length (inches)</Form.Label>
            <Form.Control
              type="number"
              step="0.1"
              name="length"
              value={formData.length}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Circumferential Extent (inches)</Form.Label>
            <Form.Control
              type="number"
              step="0.1"
              name="circumferentialExtent"
              value={formData.circumferentialExtent}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
      </Row>

      <h6 className="text-primary mb-3">Design Parameters</h6>
      <Row>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Design Pressure (psi)</Form.Label>
            <Form.Control
              type="number"
              step="0.1"
              name="designPressure"
              value={formData.designPressure}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Allowable Stress (psi)</Form.Label>
            <Form.Control
              type="number"
              step="1"
              name="allowableStress"
              value={formData.allowableStress}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Weld Efficiency</Form.Label>
            <Form.Control
              type="number"
              step="0.01"
              min="0.1"
              max="1.0"
              name="weldEfficiency"
              value={formData.weldEfficiency}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Future Corrosion Allowance (inches)</Form.Label>
            <Form.Control
              type="number"
              step="0.01"
              name="futureCorrosionAllowance"
              value={formData.futureCorrosionAllowance}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
      </Row>
    </>
  );

  const renderPart6Form = () => (
    <>
      <h6 className="text-primary mb-3">Vessel Geometry</h6>
      <Row>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Inside Diameter (inches)</Form.Label>
            <Form.Control
              type="number"
              step="0.1"
              name="insideDiameter"
              value={formData.insideDiameter}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Wall Thickness (inches)</Form.Label>
            <Form.Control
              type="number"
              step="0.001"
              name="wallThickness"
              value={formData.wallThickness}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
      </Row>

      <h6 className="text-primary mb-3">Pitting Characteristics</h6>
      <Row>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Pit Depth (inches)</Form.Label>
            <Form.Control
              type="number"
              step="0.001"
              name="pitDepth"
              value={formData.pitDepth}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Pit Diameter (inches)</Form.Label>
            <Form.Control
              type="number"
              step="0.01"
              name="pitDiameter"
              value={formData.pitDiameter}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Number of Pits</Form.Label>
            <Form.Control
              type="number"
              step="1"
              min="1"
              name="numberOfPits"
              value={formData.numberOfPits}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Pit Spacing (inches)</Form.Label>
            <Form.Control
              type="number"
              step="0.1"
              name="pitSpacing"
              value={formData.pitSpacing}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
      </Row>

      <h6 className="text-primary mb-3">Design Parameters</h6>
      <Row>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Design Pressure (psi)</Form.Label>
            <Form.Control
              type="number"
              step="0.1"
              name="designPressure"
              value={formData.designPressure}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Allowable Stress (psi)</Form.Label>
            <Form.Control
              type="number"
              step="1"
              name="allowableStress"
              value={formData.allowableStress}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Weld Efficiency</Form.Label>
            <Form.Control
              type="number"
              step="0.01"
              min="0.1"
              max="1.0"
              name="weldEfficiency"
              value={formData.weldEfficiency}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>Future Corrosion Allowance (inches)</Form.Label>
            <Form.Control
              type="number"
              step="0.01"
              name="futureCorrosionAllowance"
              value={formData.futureCorrosionAllowance}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
        </Col>
      </Row>
    </>
  );

  const renderResults = () => {
    if (!result) return null;

    const { calculation } = result;

    return (
      <Card className="mt-4">
        <Card.Header>
          <h5>API 579-1 Assessment Results</h5>
        </Card.Header>
        <Card.Body>
          <Alert variant={calculation.acceptable ? 'success' : 'danger'}>
            <strong>Result: {calculation.acceptable ? 'ACCEPTABLE' : 'NOT ACCEPTABLE'}</strong>
            <br />
            <small>{calculation.notes}</small>
          </Alert>

          <Row>
            <Col md={6}>
              <h6 className="text-primary">Calculation Summary</h6>
              <Table striped bordered hover size="sm">
                <tbody>
                  <tr>
                    <td><strong>Assessment Level</strong></td>
                    <td>{calculation.assessmentLevel}</td>
                  </tr>
                  <tr>
                    <td><strong>Calculation Method</strong></td>
                    <td>{calculation.calculationMethod}</td>
                  </tr>
                  <tr>
                    <td><strong>Standard</strong></td>
                    <td>{calculation.standard}</td>
                  </tr>
                  {calculation.treq && (
                    <tr>
                      <td><strong>Required Thickness (t_req)</strong></td>
                      <td>{calculation.treq} inches</td>
                    </tr>
                  )}
                  {calculation.mawp && (
                    <tr>
                      <td><strong>MAWP</strong></td>
                      <td>{calculation.mawp} psi</td>
                    </tr>
                  )}
                </tbody>
              </Table>
            </Col>

            <Col md={6}>
              <h6 className="text-primary">Assessment Parameters</h6>
              <Table striped bordered hover size="sm">
                <tbody>
                  {calculation.remainingThickness && (
                    <tr>
                      <td><strong>Remaining Thickness</strong></td>
                      <td>{calculation.remainingThickness} inches</td>
                    </tr>
                  )}
                  {calculation.tcmin && (
                    <tr>
                      <td><strong>Min. Thickness (t_cmin)</strong></td>
                      <td>{calculation.tcmin} inches</td>
                    </tr>
                  )}
                  {calculation.safetyFactor && (
                    <tr>
                      <td><strong>Safety Factor</strong></td>
                      <td>{calculation.safetyFactor}</td>
                    </tr>
                  )}
                  {calculation.remainingLife && (
                    <tr>
                      <td><strong>Remaining Life</strong></td>
                      <td>{calculation.remainingLife} years</td>
                    </tr>
                  )}
                  {calculation.rsf && (
                    <tr>
                      <td><strong>RSF</strong></td>
                      <td>{calculation.rsf}</td>
                    </tr>
                  )}
                </tbody>
              </Table>
            </Col>
          </Row>

          {/* Part 5 Specific Results */}
          {calculation.thicknessRatio && (
            <Row className="mt-3">
              <Col md={12}>
                <h6 className="text-primary">Local Thin Area Analysis</h6>
                <Table striped bordered hover size="sm">
                  <tbody>
                    <tr>
                      <td><strong>Thickness Ratio</strong></td>
                      <td>{calculation.thicknessRatio}</td>
                    </tr>
                    <tr>
                      <td><strong>Assessment Length</strong></td>
                      <td>{calculation.assessmentLength} inches</td>
                    </tr>
                    <tr>
                      <td><strong>Critical Length</strong></td>
                      <td>{calculation.criticalLength} inches</td>
                    </tr>
                    <tr>
                      <td><strong>Geometric Check</strong></td>
                      <td>{calculation.geometricCheck ? 'PASS' : 'FAIL'}</td>
                    </tr>
                  </tbody>
                </Table>
              </Col>
            </Row>
          )}

          {/* Part 6 Specific Results */}
          {calculation.depthRatio && (
            <Row className="mt-3">
              <Col md={12}>
                <h6 className="text-primary">Pitting Corrosion Analysis</h6>
                <Table striped bordered hover size="sm">
                  <tbody>
                    <tr>
                      <td><strong>Depth Ratio</strong></td>
                      <td>{calculation.depthRatio}</td>
                    </tr>
                    <tr>
                      <td><strong>Max Allowable Depth</strong></td>
                      <td>{calculation.maxAllowableDepth} inches</td>
                    </tr>
                    <tr>
                      <td><strong>Diameter/Thickness Ratio</strong></td>
                      <td>{calculation.diameterToThicknessRatio}</td>
                    </tr>
                    <tr>
                      <td><strong>Pit Density</strong></td>
                      <td>{calculation.pitDensity} pits/in²</td>
                    </tr>
                    <tr>
                      <td><strong>Individual Checks</strong></td>
                      <td>
                        Depth: {calculation.depthCheck ? '✓' : '✗'} |
                        Diameter: {calculation.diameterCheck ? '✓' : '✗'} |
                        RSF: {calculation.rsfCheck ? '✓' : '✗'} |
                        Spacing: {calculation.spacingCheck ? '✓' : '✗'}
                      </td>
                    </tr>
                  </tbody>
                </Table>
              </Col>
            </Row>
          )}
        </Card.Body>
      </Card>
    );
  };

  return (
    <Container>
      <Card>
        <Card.Header>
          <h5>Create New FFS Assessment</h5>
        </Card.Header>
        <Card.Body>
          {error && <Alert variant="danger">{error}</Alert>}

          <Form onSubmit={handleSubmit}>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Equipment ID</Form.Label>
                  <Form.Control
                    type="text"
                    name="equipmentId"
                    value={formData.equipmentId}
                    onChange={handleInputChange}
                    required
                    placeholder="e.g., TANK-001"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Assessment Type</Form.Label>
                  <Form.Select
                    name="assessmentType"
                    value={formData.assessmentType}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="PART_4_GENERAL_METAL_LOSS">Part 4 - General Metal Loss</option>
                    <option value="PART_5_LOCAL_THIN_AREAS">Part 5 - Local Thin Areas</option>
                    <option value="PART_6_PITTING_CORROSION">Part 6 - Pitting Corrosion</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Material</Form.Label>
                  <Form.Select
                    name="materialId"
                    value={formData.materialId}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select Material</option>
                    {materials.map(material => (
                      <option key={material.id} value={material.id}>
                        {material.name} - {material.specification}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <hr />
            <h6>Assessment Parameters</h6>

            {formData.assessmentType === 'PART_4_GENERAL_METAL_LOSS' && renderPart4Form()}
            {formData.assessmentType === 'PART_5_LOCAL_THIN_AREAS' && renderPart5Form()}
            {formData.assessmentType === 'PART_6_PITTING_CORROSION' && renderPart6Form()}

            <div className="d-grid gap-2 mt-4">
              <Button
                variant="primary"
                type="submit"
                disabled={loading}
                size="lg"
              >
                {loading ? 'Calculating...' : 'Perform Assessment'}
              </Button>
            </div>
          </Form>

          {renderResults()}
        </Card.Body>
      </Card>
    </Container>
  );
};

export default AssessmentForm;
