import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, <PERSON>, Button, Alert } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import assessmentService from '../services/assessmentService';

/**
 * Dashboard component showing overview of FFS assessments
 */
const Dashboard = () => {
  const [statistics, setStatistics] = useState(null);
  const [recentAssessments, setRecentAssessments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Load statistics and recent assessments
      const [statsResponse, assessmentsResponse] = await Promise.all([
        assessmentService.getStatistics(),
        assessmentService.getAllAssessments()
      ]);
      
      setStatistics(statsResponse.data);
      // Get the 5 most recent assessments
      const recent = assessmentsResponse.data
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5);
      setRecentAssessments(recent);
      
    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setError('Failed to load dashboard data');
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'DRAFT': return 'status-draft';
      case 'IN_PROGRESS': return 'status-in-progress';
      case 'COMPLETED': return 'status-completed';
      case 'REVIEWED': return 'status-reviewed';
      case 'APPROVED': return 'status-approved';
      default: return 'bg-secondary';
    }
  };

  const getAcceptabilityClass = (isAcceptable) => {
    if (isAcceptable === null || isAcceptable === undefined) return '';
    return isAcceptable ? 'assessment-acceptable' : 'assessment-not-acceptable';
  };

  if (loading) {
    return (
      <Container>
        <div className="loading-spinner">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <Alert variant="danger">
          <Alert.Heading>Error</Alert.Heading>
          <p>{error}</p>
          <Button variant="outline-danger" onClick={loadDashboardData}>
            Try Again
          </Button>
        </Alert>
      </Container>
    );
  }

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h1 className="h3 mb-0">FFS Assessment Dashboard</h1>
          <p className="text-muted">API 579-1 / ASME FFS-1 Compliant Assessment System</p>
        </Col>
        <Col xs="auto">
          <Link to="/assessments/new">
            <Button variant="primary" size="lg">
              <i className="fas fa-plus me-2"></i>
              New Assessment
            </Button>
          </Link>
        </Col>
      </Row>

      {/* Statistics Cards */}
      {statistics && (
        <Row className="mb-4">
          <Col md={3} sm={6} className="mb-3">
            <Card className="metric-card">
              <Card.Body>
                <div className="metric-value text-primary">{statistics.totalAssessments}</div>
                <div className="metric-label">Total Assessments</div>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} sm={6} className="mb-3">
            <Card className="metric-card">
              <Card.Body>
                <div className="metric-value text-success">{statistics.completedAssessments}</div>
                <div className="metric-label">Completed</div>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} sm={6} className="mb-3">
            <Card className="metric-card">
              <Card.Body>
                <div className="metric-value text-warning">{statistics.acceptableAssessments}</div>
                <div className="metric-label">Acceptable</div>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} sm={6} className="mb-3">
            <Card className="metric-card">
              <Card.Body>
                <div className="metric-value text-info">
                  {statistics.totalAssessments - statistics.acceptableAssessments}
                </div>
                <div className="metric-label">Requiring Attention</div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      <Row>
        {/* Assessment Type Distribution */}
        {statistics && (
          <Col lg={6} className="mb-4">
            <Card>
              <Card.Header>
                <h5 className="mb-0">Assessment Types</h5>
              </Card.Header>
              <Card.Body>
                <div className="mb-3">
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <span>Part 4 - General Metal Loss</span>
                    <span className="badge bg-primary">{statistics.part4Assessments}</span>
                  </div>
                  <div className="progress mb-3">
                    <div 
                      className="progress-bar bg-primary" 
                      style={{width: `${(statistics.part4Assessments / statistics.totalAssessments) * 100}%`}}
                    ></div>
                  </div>
                </div>
                
                <div className="mb-3">
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <span>Part 5 - Local Thin Areas</span>
                    <span className="badge bg-success">{statistics.part5Assessments}</span>
                  </div>
                  <div className="progress mb-3">
                    <div 
                      className="progress-bar bg-success" 
                      style={{width: `${(statistics.part5Assessments / statistics.totalAssessments) * 100}%`}}
                    ></div>
                  </div>
                </div>
                
                <div className="mb-3">
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <span>Part 6 - Pitting Corrosion</span>
                    <span className="badge bg-warning">{statistics.part6Assessments}</span>
                  </div>
                  <div className="progress">
                    <div 
                      className="progress-bar bg-warning" 
                      style={{width: `${(statistics.part6Assessments / statistics.totalAssessments) * 100}%`}}
                    ></div>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>
        )}

        {/* Recent Assessments */}
        <Col lg={6} className="mb-4">
          <Card>
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0">Recent Assessments</h5>
              <Link to="/assessments" className="btn btn-sm btn-outline-primary">
                View All
              </Link>
            </Card.Header>
            <Card.Body>
              {recentAssessments.length === 0 ? (
                <p className="text-muted text-center py-3">No assessments found</p>
              ) : (
                <div className="list-group list-group-flush">
                  {recentAssessments.map(assessment => (
                    <div key={assessment.id} className="list-group-item border-0 px-0">
                      <div className="d-flex justify-content-between align-items-start">
                        <div className="flex-grow-1">
                          <Link 
                            to={`/assessments/${assessment.id}`} 
                            className="text-decoration-none"
                          >
                            <h6 className="mb-1">{assessment.name}</h6>
                          </Link>
                          <p className="mb-1 text-muted small">
                            {assessment.componentType} - {assessment.assessmentTypeDescription}
                          </p>
                          <small className="text-muted">
                            Created: {new Date(assessment.createdAt).toLocaleDateString()}
                          </small>
                        </div>
                        <div className="text-end">
                          <span className={`badge ${getStatusBadgeClass(assessment.status)} mb-1`}>
                            {assessment.status}
                          </span>
                          {assessment.isAcceptable !== null && (
                            <div className={`small ${getAcceptabilityClass(assessment.isAcceptable)}`}>
                              {assessment.isAcceptable ? 'Acceptable' : 'Not Acceptable'}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Quick Actions */}
      <Row>
        <Col>
          <Card>
            <Card.Header>
              <h5 className="mb-0">Quick Actions</h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={3} sm={6} className="mb-3">
                  <Link to="/assessments/new" className="text-decoration-none">
                    <Card className="h-100 text-center border-primary">
                      <Card.Body>
                        <i className="fas fa-plus-circle fa-2x text-primary mb-2"></i>
                        <h6>New Assessment</h6>
                        <small className="text-muted">Start a new FFS assessment</small>
                      </Card.Body>
                    </Card>
                  </Link>
                </Col>
                
                <Col md={3} sm={6} className="mb-3">
                  <Link to="/materials" className="text-decoration-none">
                    <Card className="h-100 text-center border-success">
                      <Card.Body>
                        <i className="fas fa-database fa-2x text-success mb-2"></i>
                        <h6>Material Database</h6>
                        <small className="text-muted">Manage material properties</small>
                      </Card.Body>
                    </Card>
                  </Link>
                </Col>
                
                <Col md={3} sm={6} className="mb-3">
                  <Link to="/reports" className="text-decoration-none">
                    <Card className="h-100 text-center border-warning">
                      <Card.Body>
                        <i className="fas fa-file-pdf fa-2x text-warning mb-2"></i>
                        <h6>Generate Reports</h6>
                        <small className="text-muted">Create assessment reports</small>
                      </Card.Body>
                    </Card>
                  </Link>
                </Col>
                
                <Col md={3} sm={6} className="mb-3">
                  <Card className="h-100 text-center border-info">
                    <Card.Body>
                      <i className="fas fa-upload fa-2x text-info mb-2"></i>
                      <h6>Import Data</h6>
                      <small className="text-muted">Import NDT data (CSV/Excel)</small>
                    </Card.Body>
                  </Card>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default Dashboard;
