import React from 'react';
import { Navbar, Nav, Container } from 'react-bootstrap';
import { <PERSON> } from 'react-router-dom';

/**
 * Navigation component for the FFS Assessment Application
 */
const Navigation = () => {
  return (
    <Navbar bg="dark" variant="dark" expand="lg" sticky="top">
      <Container fluid>
        <Navbar.Brand as={Link} to="/">
          <i className="fas fa-cogs me-2"></i>
          FFS Assessment
        </Navbar.Brand>
        
        <Navbar.Toggle aria-controls="basic-navbar-nav" />
        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="me-auto">
            <Nav.Link as={Link} to="/">
              <i className="fas fa-tachometer-alt me-1"></i>
              Dashboard
            </Nav.Link>

            <Nav.Link as={Link} to="/assessments">
              <i className="fas fa-clipboard-list me-1"></i>
              Assessments
            </Nav.Link>

            <Nav.Link as={Link} to="/materials">
              <i className="fas fa-database me-1"></i>
              Materials
            </Nav.Link>

            <Nav.Link as={Link} to="/reports">
              <i className="fas fa-file-pdf me-1"></i>
              Reports
            </Nav.Link>
          </Nav>

          <Nav>
            <Nav.Link href="#help">
              <i className="fas fa-question-circle me-1"></i>
              Help
            </Nav.Link>
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
};

export default Navigation;
