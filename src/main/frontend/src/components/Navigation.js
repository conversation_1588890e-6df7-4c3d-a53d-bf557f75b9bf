import React from 'react';
import { Navbar, Nav, Container } from 'react-bootstrap';
import { LinkContainer } from 'react-router-bootstrap';

/**
 * Navigation component for the FFS Assessment Application
 */
const Navigation = () => {
  return (
    <Navbar bg="dark" variant="dark" expand="lg" sticky="top">
      <Container fluid>
        <LinkContainer to="/">
          <Navbar.Brand>
            <i className="fas fa-cogs me-2"></i>
            FFS Assessment
          </Navbar.Brand>
        </LinkContainer>
        
        <Navbar.Toggle aria-controls="basic-navbar-nav" />
        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="me-auto">
            <LinkContainer to="/">
              <Nav.Link>
                <i className="fas fa-tachometer-alt me-1"></i>
                Dashboard
              </Nav.Link>
            </LinkContainer>
            
            <LinkContainer to="/assessments">
              <Nav.Link>
                <i className="fas fa-clipboard-list me-1"></i>
                Assessments
              </Nav.Link>
            </LinkContainer>
            
            <LinkContainer to="/materials">
              <Nav.Link>
                <i className="fas fa-database me-1"></i>
                Materials
              </Nav.Link>
            </LinkContainer>
            
            <LinkContainer to="/reports">
              <Nav.Link>
                <i className="fas fa-file-pdf me-1"></i>
                Reports
              </Nav.Link>
            </LinkContainer>
          </Nav>
          
          <Nav>
            <Nav.Link href="#help">
              <i className="fas fa-question-circle me-1"></i>
              Help
            </Nav.Link>
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
};

export default Navigation;
