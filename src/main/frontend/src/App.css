.App {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.navbar-brand {
  font-weight: bold;
  color: #2c3e50 !important;
}

.card {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: none;
  margin-bottom: 1rem;
}

.card-header {
  background-color: #3498db;
  color: white;
  font-weight: 600;
}

.btn-primary {
  background-color: #3498db;
  border-color: #3498db;
}

.btn-primary:hover {
  background-color: #2980b9;
  border-color: #2980b9;
}

.btn-success {
  background-color: #27ae60;
  border-color: #27ae60;
}

.btn-success:hover {
  background-color: #229954;
  border-color: #229954;
}

.btn-danger {
  background-color: #e74c3c;
  border-color: #e74c3c;
}

.btn-danger:hover {
  background-color: #c0392b;
  border-color: #c0392b;
}

.btn-warning {
  background-color: #f39c12;
  border-color: #f39c12;
}

.btn-warning:hover {
  background-color: #e67e22;
  border-color: #e67e22;
}

.table {
  background-color: white;
}

.table th {
  background-color: #ecf0f1;
  border-top: none;
  font-weight: 600;
}

.badge {
  font-size: 0.75em;
}

.status-draft {
  background-color: #95a5a6;
}

.status-in-progress {
  background-color: #f39c12;
}

.status-completed {
  background-color: #27ae60;
}

.status-reviewed {
  background-color: #3498db;
}

.status-approved {
  background-color: #8e44ad;
}

.assessment-acceptable {
  color: #27ae60;
  font-weight: bold;
}

.assessment-not-acceptable {
  color: #e74c3c;
  font-weight: bold;
}

.form-section {
  background-color: white;
  padding: 1.5rem;
  margin-bottom: 1rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-section h5 {
  color: #2c3e50;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #ecf0f1;
}

.calculation-result {
  background-color: #f8f9fa;
  border-left: 4px solid #3498db;
  padding: 1rem;
  margin: 1rem 0;
}

.calculation-result.acceptable {
  border-left-color: #27ae60;
  background-color: #d5f4e6;
}

.calculation-result.not-acceptable {
  border-left-color: #e74c3c;
  background-color: #fdeaea;
}

.metric-card {
  text-align: center;
  padding: 1.5rem;
}

.metric-value {
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
}

.metric-label {
  color: #7f8c8d;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.error-message {
  background-color: #fdeaea;
  border: 1px solid #e74c3c;
  color: #c0392b;
  padding: 1rem;
  border-radius: 0.375rem;
  margin: 1rem 0;
}

.success-message {
  background-color: #d5f4e6;
  border: 1px solid #27ae60;
  color: #229954;
  padding: 1rem;
  border-radius: 0.375rem;
  margin: 1rem 0;
}

.thickness-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.5rem;
  margin: 1rem 0;
}

.thickness-reading {
  background-color: #ecf0f1;
  padding: 0.5rem;
  text-align: center;
  border-radius: 0.25rem;
  font-size: 0.9rem;
}

.thickness-reading.minimum {
  background-color: #e74c3c;
  color: white;
  font-weight: bold;
}

.pitting-visualization {
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  padding: 1rem;
  margin: 1rem 0;
}

.material-property {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #ecf0f1;
}

.material-property:last-child {
  border-bottom: none;
}

.property-name {
  font-weight: 600;
  color: #2c3e50;
}

.property-value {
  color: #7f8c8d;
}

@media (max-width: 768px) {
  .container-fluid {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  
  .form-section {
    padding: 1rem;
  }
  
  .metric-value {
    font-size: 1.5rem;
  }
  
  .thickness-grid {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  }
}
