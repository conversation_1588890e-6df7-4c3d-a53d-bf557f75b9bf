{"name": "ffs-frontend", "version": "1.0.0", "description": "Fitness for Service Assessment Application Frontend", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "axios": "^1.6.2", "bootstrap": "^5.3.2", "react-bootstrap": "^2.9.1", "react-chartjs-2": "^5.2.0", "chart.js": "^4.4.0", "formik": "^2.4.5", "yup": "^1.3.3", "react-table": "^7.8.0", "react-select": "^5.8.0", "react-datepicker": "^4.24.0", "react-dropzone": "^14.2.3", "react-toastify": "^9.1.3", "lodash": "^4.17.21", "moment": "^2.29.4", "file-saver": "^2.0.5", "jspdf": "^2.5.1", "html2canvas": "^1.4.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8080", "devDependencies": {"@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "@types/lodash": "^4.14.202", "@types/file-saver": "^2.0.7"}}