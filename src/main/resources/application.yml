server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: fitness-for-service
  
  datasource:
    url: ***************************************
    username: ffs_user
    password: ffs_password
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  security:
    jwt:
      secret: ${JWT_SECRET:ffs-secret-key-change-in-production}
      expiration: 86400000 # 24 hours
  
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  
  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: UTC

logging:
  level:
    com.ffs: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/ffs-application.log

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized

# FFS Application Specific Configuration
ffs:
  calculation:
    precision: 6
    rounding-mode: HALF_UP
  
  materials:
    database-path: classpath:materials/
    auto-load: true
  
  reports:
    output-directory: ${user.home}/ffs-reports
    template-directory: classpath:templates/reports/
  
  file-upload:
    allowed-extensions: csv,xlsx,xls
    max-size: 10MB
  
  api:
    version: v1
    base-path: /api/v1

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: jdbc:h2:mem:ffs_dev_db
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
  
  h2:
    console:
      enabled: true
      path: /h2-console

logging:
  level:
    root: INFO
    com.ffs: DEBUG

---
# Test Profile  
spring:
  config:
    activate:
      on-profile: test
  
  datasource:
    url: jdbc:h2:mem:ffs_test_db
    driver-class-name: org.h2.Driver
    username: sa
    password:
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false

logging:
  level:
    root: WARN
    com.ffs: INFO

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

logging:
  level:
    root: WARN
    com.ffs: INFO
  file:
    name: /var/log/ffs/application.log
