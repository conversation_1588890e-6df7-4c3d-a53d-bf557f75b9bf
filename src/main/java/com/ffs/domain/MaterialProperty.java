package com.ffs.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * Material property entity for temperature-dependent material properties
 * Used for storing allowable stress, yield strength, etc. at different temperatures
 */
@Entity
@Table(name = "material_properties", indexes = {
    @Index(name = "idx_material_property_type", columnList = "property_type"),
    @Index(name = "idx_material_property_temp", columnList = "temperature"),
    @Index(name = "idx_material_id", columnList = "material_id")
})
public class MaterialProperty {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "material_id", nullable = false)
    private Material material;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "property_type", nullable = false)
    private Material.PropertyType propertyType;
    
    @Column(name = "temperature")
    private Double temperature; // °F (null for temperature-independent properties)
    
    @NotNull
    @Column(name = "value", nullable = false)
    private Double value;
    
    @Column(name = "unit", length = 20)
    private String unit; // e.g., "psi", "ksi", "MPa"
    
    @Column(name = "source", length = 100)
    private String source; // e.g., "ASME SEC II-D"
    
    @Column(name = "notes", length = 200)
    private String notes;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public MaterialProperty() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public MaterialProperty(Material material, Material.PropertyType propertyType, Double value) {
        this();
        this.material = material;
        this.propertyType = propertyType;
        this.value = value;
    }
    
    public MaterialProperty(Material material, Material.PropertyType propertyType, Double temperature, Double value, String unit) {
        this(material, propertyType, value);
        this.temperature = temperature;
        this.unit = unit;
    }
    
    // Lifecycle callbacks
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // Business methods
    
    /**
     * Check if this property applies to the given temperature
     * @param temp Temperature to check
     * @param tolerance Temperature tolerance
     * @return true if property applies
     */
    public boolean appliesTo(double temp, double tolerance) {
        if (temperature == null) {
            return true; // Temperature-independent property
        }
        return Math.abs(temperature - temp) <= tolerance;
    }
    
    /**
     * Get value in specified unit (basic unit conversion)
     * @param targetUnit Target unit
     * @return Converted value
     */
    public Double getValueInUnit(String targetUnit) {
        if (unit == null || unit.equals(targetUnit)) {
            return value;
        }
        
        // Basic unit conversions for stress/pressure
        if (isStressProperty()) {
            return convertStressValue(value, unit, targetUnit);
        }
        
        return value; // No conversion available
    }
    
    private boolean isStressProperty() {
        return propertyType == Material.PropertyType.ALLOWABLE_STRESS ||
               propertyType == Material.PropertyType.YIELD_STRENGTH ||
               propertyType == Material.PropertyType.TENSILE_STRENGTH;
    }
    
    private Double convertStressValue(Double value, String fromUnit, String toUnit) {
        // Convert to PSI first, then to target unit
        double psiValue = convertToPsi(value, fromUnit);
        return convertFromPsi(psiValue, toUnit);
    }
    
    private double convertToPsi(Double value, String unit) {
        switch (unit.toLowerCase()) {
            case "psi": return value;
            case "ksi": return value * 1000;
            case "mpa": return value * 145.038; // MPa to PSI
            case "kpa": return value * 0.145038; // kPa to PSI
            default: return value;
        }
    }
    
    private double convertFromPsi(double psiValue, String toUnit) {
        switch (toUnit.toLowerCase()) {
            case "psi": return psiValue;
            case "ksi": return psiValue / 1000;
            case "mpa": return psiValue / 145.038; // PSI to MPa
            case "kpa": return psiValue / 0.145038; // PSI to kPa
            default: return psiValue;
        }
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Material getMaterial() { return material; }
    public void setMaterial(Material material) { this.material = material; }
    
    public Material.PropertyType getPropertyType() { return propertyType; }
    public void setPropertyType(Material.PropertyType propertyType) { this.propertyType = propertyType; }
    
    public Double getTemperature() { return temperature; }
    public void setTemperature(Double temperature) { this.temperature = temperature; }
    
    public Double getValue() { return value; }
    public void setValue(Double value) { this.value = value; }
    
    public String getUnit() { return unit; }
    public void setUnit(String unit) { this.unit = unit; }
    
    public String getSource() { return source; }
    public void setSource(String source) { this.source = source; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    @Override
    public String toString() {
        return String.format("MaterialProperty{id=%d, type=%s, temp=%s, value=%s %s}", 
            id, propertyType, temperature, value, unit);
    }
}
