package com.ffs.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Material entity representing ASME/API material specifications
 * with temperature-dependent properties for FFS assessments
 */
@Entity
@Table(name = "materials", indexes = {
    @Index(name = "idx_material_specification", columnList = "specification"),
    @Index(name = "idx_material_grade", columnList = "grade"),
    @Index(name = "idx_material_curve", columnList = "fatigue_curve")
})
public class Material {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Column(name = "specification", nullable = false, length = 50)
    private String specification; // e.g., "SA-516"
    
    @NotBlank
    @Column(name = "grade", nullable = false, length = 50)
    private String grade; // e.g., "Grade 70"
    
    @Column(name = "year_edition")
    private Integer yearEdition; // e.g., 2007
    
    @NotBlank
    @Column(name = "material_type", nullable = false, length = 100)
    private String materialType; // e.g., "Carbon Steel"
    
    @Column(name = "condition_description", length = 200)
    private String conditionDescription; // e.g., "Normalized and tempered"
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "fatigue_curve", nullable = false)
    private FatigueCurve fatigueCurve; // A, B, C, D
    
    @Positive
    @Column(name = "min_temperature")
    private Double minTemperature; // °F
    
    @Positive
    @Column(name = "max_temperature")
    private Double maxTemperature; // °F
    
    @Column(name = "is_fine_grain_practice")
    private Boolean isFineGrainPractice = false;
    
    @Column(name = "is_normalized")
    private Boolean isNormalized = false;
    
    @Column(name = "is_obsolete")
    private Boolean isObsolete = false;
    
    @Column(name = "notes", length = 500)
    private String notes;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @OneToMany(mappedBy = "material", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<MaterialProperty> properties = new ArrayList<>();
    
    // Constructors
    public Material() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public Material(String specification, String grade, FatigueCurve fatigueCurve) {
        this();
        this.specification = specification;
        this.grade = grade;
        this.fatigueCurve = fatigueCurve;
    }
    
    // Lifecycle callbacks
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // Business methods
    
    /**
     * Get allowable stress at specified temperature
     * @param temperature Temperature in °F
     * @return Allowable stress in psi, or null if not found
     */
    public Double getAllowableStress(double temperature) {
        return properties.stream()
            .filter(prop -> prop.getPropertyType() == PropertyType.ALLOWABLE_STRESS)
            .filter(prop -> prop.getTemperature() != null)
            .filter(prop -> Math.abs(prop.getTemperature() - temperature) < 0.1)
            .map(MaterialProperty::getValue)
            .findFirst()
            .orElse(null);
    }
    
    /**
     * Get yield strength at specified temperature
     * @param temperature Temperature in °F
     * @return Yield strength in psi, or null if not found
     */
    public Double getYieldStrength(double temperature) {
        return properties.stream()
            .filter(prop -> prop.getPropertyType() == PropertyType.YIELD_STRENGTH)
            .filter(prop -> prop.getTemperature() != null)
            .filter(prop -> Math.abs(prop.getTemperature() - temperature) < 0.1)
            .map(MaterialProperty::getValue)
            .findFirst()
            .orElse(null);
    }
    
    /**
     * Get tensile strength at specified temperature
     * @param temperature Temperature in °F
     * @return Tensile strength in psi, or null if not found
     */
    public Double getTensileStrength(double temperature) {
        return properties.stream()
            .filter(prop -> prop.getPropertyType() == PropertyType.TENSILE_STRENGTH)
            .filter(prop -> prop.getTemperature() != null)
            .filter(prop -> Math.abs(prop.getTemperature() - temperature) < 0.1)
            .map(MaterialProperty::getValue)
            .findFirst()
            .orElse(null);
    }
    
    /**
     * Check if material is suitable for given temperature range
     * @param minTemp Minimum operating temperature
     * @param maxTemp Maximum operating temperature
     * @return true if material is suitable
     */
    public boolean isSuitableForTemperatureRange(double minTemp, double maxTemp) {
        return (minTemperature == null || minTemp >= minTemperature) &&
               (maxTemperature == null || maxTemp <= maxTemperature);
    }
    
    /**
     * Get full material designation
     * @return Full material name (e.g., "SA-516 Grade 70")
     */
    public String getFullDesignation() {
        return specification + " " + grade;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getSpecification() { return specification; }
    public void setSpecification(String specification) { this.specification = specification; }
    
    public String getGrade() { return grade; }
    public void setGrade(String grade) { this.grade = grade; }
    
    public Integer getYearEdition() { return yearEdition; }
    public void setYearEdition(Integer yearEdition) { this.yearEdition = yearEdition; }
    
    public String getMaterialType() { return materialType; }
    public void setMaterialType(String materialType) { this.materialType = materialType; }
    
    public String getConditionDescription() { return conditionDescription; }
    public void setConditionDescription(String conditionDescription) { this.conditionDescription = conditionDescription; }
    
    public FatigueCurve getFatigueCurve() { return fatigueCurve; }
    public void setFatigueCurve(FatigueCurve fatigueCurve) { this.fatigueCurve = fatigueCurve; }
    
    public Double getMinTemperature() { return minTemperature; }
    public void setMinTemperature(Double minTemperature) { this.minTemperature = minTemperature; }
    
    public Double getMaxTemperature() { return maxTemperature; }
    public void setMaxTemperature(Double maxTemperature) { this.maxTemperature = maxTemperature; }
    
    public Boolean getIsFineGrainPractice() { return isFineGrainPractice; }
    public void setIsFineGrainPractice(Boolean isFineGrainPractice) { this.isFineGrainPractice = isFineGrainPractice; }
    
    public Boolean getIsNormalized() { return isNormalized; }
    public void setIsNormalized(Boolean isNormalized) { this.isNormalized = isNormalized; }
    
    public Boolean getIsObsolete() { return isObsolete; }
    public void setIsObsolete(Boolean isObsolete) { this.isObsolete = isObsolete; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    public List<MaterialProperty> getProperties() { return properties; }
    public void setProperties(List<MaterialProperty> properties) { this.properties = properties; }
    
    // Enums
    public enum FatigueCurve {
        A, B, C, D
    }
    
    public enum PropertyType {
        ALLOWABLE_STRESS,
        YIELD_STRENGTH,
        TENSILE_STRENGTH,
        ELASTIC_MODULUS,
        POISSON_RATIO,
        THERMAL_EXPANSION,
        THERMAL_CONDUCTIVITY
    }
}
