package com.ffs.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.time.LocalDateTime;

/**
 * Thickness reading entity for storing NDT thickness measurements
 * Used for Critical Thickness Profile (CTP) analysis in Parts 4 and 5
 */
@Entity
@Table(name = "thickness_readings", indexes = {
    @Index(name = "idx_thickness_assessment", columnList = "assessment_id"),
    @Index(name = "idx_thickness_location", columnList = "location_x, location_y"),
    @Index(name = "idx_thickness_sequence", columnList = "sequence_number")
})
public class ThicknessReading {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assessment_id", nullable = false)
    private Assessment assessment;
    
    @NotNull
    @Column(name = "sequence_number", nullable = false)
    private Integer sequenceNumber; // Reading sequence (1, 2, 3, ...)
    
    @Column(name = "location_x")
    private Double locationX; // X coordinate (inches or mm)
    
    @Column(name = "location_y")
    private Double locationY; // Y coordinate (inches or mm)
    
    @Column(name = "location_description", length = 200)
    private String locationDescription; // e.g., "12 o'clock position"
    
    @Positive
    @Column(name = "thickness", nullable = false)
    private Double thickness; // Measured thickness (inches)
    
    @Column(name = "original_thickness")
    private Double originalThickness; // Original/nominal thickness at this location
    
    @Column(name = "metal_loss")
    private Double metalLoss; // Calculated metal loss
    
    @Enumerated(EnumType.STRING)
    @Column(name = "measurement_method")
    private MeasurementMethod measurementMethod;
    
    @Column(name = "measurement_date")
    private LocalDateTime measurementDate;
    
    @Column(name = "inspector", length = 100)
    private String inspector;
    
    @Column(name = "equipment_used", length = 100)
    private String equipmentUsed; // e.g., "UT Gauge Model XYZ"
    
    @Column(name = "surface_condition", length = 100)
    private String surfaceCondition; // e.g., "Cleaned", "As-found"
    
    @Column(name = "temperature_during_measurement")
    private Double temperatureDuringMeasurement; // °F
    
    @Column(name = "notes", length = 500)
    private String notes;
    
    @Column(name = "is_minimum_reading")
    private Boolean isMinimumReading = false; // Flag for tmm identification
    
    @Column(name = "is_excluded")
    private Boolean isExcluded = false; // Exclude from calculations
    
    @Column(name = "exclusion_reason", length = 200)
    private String exclusionReason;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public ThicknessReading() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public ThicknessReading(Assessment assessment, Integer sequenceNumber, Double thickness) {
        this();
        this.assessment = assessment;
        this.sequenceNumber = sequenceNumber;
        this.thickness = thickness;
    }
    
    public ThicknessReading(Assessment assessment, Integer sequenceNumber, Double locationX, Double locationY, Double thickness) {
        this(assessment, sequenceNumber, thickness);
        this.locationX = locationX;
        this.locationY = locationY;
    }
    
    // Lifecycle callbacks
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    @PostLoad
    @PostPersist
    @PostUpdate
    public void calculateMetalLoss() {
        if (originalThickness != null && thickness != null) {
            this.metalLoss = originalThickness - thickness;
        }
    }
    
    // Business methods
    
    /**
     * Calculate metal loss percentage
     * @return Metal loss as percentage of original thickness
     */
    public Double getMetalLossPercentage() {
        if (originalThickness == null || originalThickness == 0 || metalLoss == null) {
            return null;
        }
        return (metalLoss / originalThickness) * 100.0;
    }
    
    /**
     * Check if reading is valid for calculations
     * @return true if reading should be included in calculations
     */
    public boolean isValidForCalculations() {
        return !Boolean.TRUE.equals(isExcluded) && thickness != null && thickness > 0;
    }
    
    /**
     * Get distance from another thickness reading
     * @param other Other thickness reading
     * @return Distance in same units as coordinates
     */
    public Double getDistanceFrom(ThicknessReading other) {
        if (locationX == null || locationY == null || 
            other.locationX == null || other.locationY == null) {
            return null;
        }
        
        double dx = locationX - other.locationX;
        double dy = locationY - other.locationY;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    /**
     * Check if this reading is within specified distance from a point
     * @param x X coordinate
     * @param y Y coordinate
     * @param maxDistance Maximum distance
     * @return true if within distance
     */
    public boolean isWithinDistance(double x, double y, double maxDistance) {
        if (locationX == null || locationY == null) {
            return false;
        }
        
        double dx = locationX - x;
        double dy = locationY - y;
        double distance = Math.sqrt(dx * dx + dy * dy);
        return distance <= maxDistance;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Assessment getAssessment() { return assessment; }
    public void setAssessment(Assessment assessment) { this.assessment = assessment; }
    
    public Integer getSequenceNumber() { return sequenceNumber; }
    public void setSequenceNumber(Integer sequenceNumber) { this.sequenceNumber = sequenceNumber; }
    
    public Double getLocationX() { return locationX; }
    public void setLocationX(Double locationX) { this.locationX = locationX; }
    
    public Double getLocationY() { return locationY; }
    public void setLocationY(Double locationY) { this.locationY = locationY; }
    
    public String getLocationDescription() { return locationDescription; }
    public void setLocationDescription(String locationDescription) { this.locationDescription = locationDescription; }
    
    public Double getThickness() { return thickness; }
    public void setThickness(Double thickness) { this.thickness = thickness; }
    
    public Double getOriginalThickness() { return originalThickness; }
    public void setOriginalThickness(Double originalThickness) { this.originalThickness = originalThickness; }
    
    public Double getMetalLoss() { return metalLoss; }
    public void setMetalLoss(Double metalLoss) { this.metalLoss = metalLoss; }
    
    public MeasurementMethod getMeasurementMethod() { return measurementMethod; }
    public void setMeasurementMethod(MeasurementMethod measurementMethod) { this.measurementMethod = measurementMethod; }
    
    public LocalDateTime getMeasurementDate() { return measurementDate; }
    public void setMeasurementDate(LocalDateTime measurementDate) { this.measurementDate = measurementDate; }
    
    public String getInspector() { return inspector; }
    public void setInspector(String inspector) { this.inspector = inspector; }
    
    public String getEquipmentUsed() { return equipmentUsed; }
    public void setEquipmentUsed(String equipmentUsed) { this.equipmentUsed = equipmentUsed; }
    
    public String getSurfaceCondition() { return surfaceCondition; }
    public void setSurfaceCondition(String surfaceCondition) { this.surfaceCondition = surfaceCondition; }
    
    public Double getTemperatureDuringMeasurement() { return temperatureDuringMeasurement; }
    public void setTemperatureDuringMeasurement(Double temperatureDuringMeasurement) { this.temperatureDuringMeasurement = temperatureDuringMeasurement; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public Boolean getIsMinimumReading() { return isMinimumReading; }
    public void setIsMinimumReading(Boolean isMinimumReading) { this.isMinimumReading = isMinimumReading; }
    
    public Boolean getIsExcluded() { return isExcluded; }
    public void setIsExcluded(Boolean isExcluded) { this.isExcluded = isExcluded; }
    
    public String getExclusionReason() { return exclusionReason; }
    public void setExclusionReason(String exclusionReason) { this.exclusionReason = exclusionReason; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    // Enums
    public enum MeasurementMethod {
        ULTRASONIC_THICKNESS,
        RADIOGRAPHIC,
        MAGNETIC_PARTICLE,
        DYE_PENETRANT,
        VISUAL,
        CALIPER,
        OTHER
    }
    
    @Override
    public String toString() {
        return String.format("ThicknessReading{seq=%d, location=(%.2f,%.2f), thickness=%.3f}", 
            sequenceNumber, locationX, locationY, thickness);
    }
}
