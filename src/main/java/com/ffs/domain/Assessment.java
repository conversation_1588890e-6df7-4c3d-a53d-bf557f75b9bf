package com.ffs.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Assessment entity representing a complete FFS assessment
 * Supports API 579-1 Parts 4, 5, and 6 assessments
 */
@Entity
@Table(name = "assessments", indexes = {
    @Index(name = "idx_assessment_type", columnList = "assessment_type"),
    @Index(name = "idx_assessment_status", columnList = "status"),
    @Index(name = "idx_assessment_created", columnList = "created_at")
})
public class Assessment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Column(name = "name", nullable = false, length = 200)
    private String name;
    
    @Column(name = "description", length = 1000)
    private String description;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "assessment_type", nullable = false)
    private AssessmentType assessmentType;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "assessment_level", nullable = false)
    private AssessmentLevel assessmentLevel;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private AssessmentStatus status;
    
    // Component Information
    @NotBlank
    @Column(name = "component_type", nullable = false, length = 100)
    private String componentType; // e.g., "Cylindrical Vessel", "Piping"
    
    @Column(name = "component_id", length = 100)
    private String componentId; // Equipment tag or ID
    
    @Column(name = "location", length = 200)
    private String location; // Physical location or description
    
    // Design Conditions
    @Positive
    @Column(name = "design_pressure")
    private Double designPressure; // psi
    
    @Column(name = "design_temperature")
    private Double designTemperature; // °F
    
    @Column(name = "operating_pressure")
    private Double operatingPressure; // psi
    
    @Column(name = "operating_temperature")
    private Double operatingTemperature; // °F
    
    // Geometry
    @Positive
    @Column(name = "inside_diameter")
    private Double insideDiameter; // inches
    
    @Positive
    @Column(name = "nominal_thickness")
    private Double nominalThickness; // inches
    
    @Column(name = "vessel_height")
    private Double vesselHeight; // inches
    
    @Column(name = "vessel_length")
    private Double vesselLength; // inches
    
    // Material and Corrosion
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "material_id")
    private Material material;
    
    @Column(name = "uniform_metal_loss")
    private Double uniformMetalLoss; // inches
    
    @Column(name = "future_corrosion_allowance")
    private Double futureCorrosionAllowance; // inches
    
    @Column(name = "corrosion_rate")
    private Double corrosionRate; // inches/year
    
    // Weld Efficiency
    @Column(name = "longitudinal_weld_efficiency")
    private Double longitudinalWeldEfficiency = 1.0;
    
    @Column(name = "circumferential_weld_efficiency")
    private Double circumferentialWeldEfficiency = 1.0;
    
    // Assessment Results
    @Column(name = "is_acceptable")
    private Boolean isAcceptable;
    
    @Column(name = "mawp")
    private Double mawp; // psi
    
    @Column(name = "mawp_reduced")
    private Double mawpReduced; // psi
    
    @Column(name = "remaining_life")
    private Double remainingLife; // years
    
    @Column(name = "rsf")
    private Double rsf; // Remaining Strength Factor
    
    @Column(name = "assessment_notes", length = 2000)
    private String assessmentNotes;
    
    // Audit fields
    @Column(name = "created_by", length = 100)
    private String createdBy;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "assessed_at")
    private LocalDateTime assessedAt;
    
    @OneToMany(mappedBy = "assessment", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ThicknessReading> thicknessReadings = new ArrayList<>();
    
    @OneToMany(mappedBy = "assessment", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<PittingData> pittingData = new ArrayList<>();
    
    // Constructors
    public Assessment() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.status = AssessmentStatus.DRAFT;
    }
    
    public Assessment(String name, AssessmentType assessmentType, AssessmentLevel assessmentLevel) {
        this();
        this.name = name;
        this.assessmentType = assessmentType;
        this.assessmentLevel = assessmentLevel;
    }
    
    // Lifecycle callbacks
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // Business methods
    
    /**
     * Mark assessment as completed
     */
    public void complete() {
        this.status = AssessmentStatus.COMPLETED;
        this.assessedAt = LocalDateTime.now();
    }
    
    /**
     * Check if assessment is acceptable for design conditions
     * @return true if acceptable
     */
    public boolean isAcceptableForDesignConditions() {
        if (mawpReduced != null && designPressure != null) {
            return mawpReduced >= designPressure;
        }
        return isAcceptable != null && isAcceptable;
    }
    
    /**
     * Get effective diameter for calculations
     * @return Diameter including corrosion allowances
     */
    public Double getEffectiveDiameter() {
        if (insideDiameter == null) return null;
        
        double corrosionAdjustment = 0.0;
        if (uniformMetalLoss != null) corrosionAdjustment += uniformMetalLoss;
        if (futureCorrosionAllowance != null) corrosionAdjustment += futureCorrosionAllowance;
        
        return insideDiameter + 2 * corrosionAdjustment;
    }
    
    /**
     * Get effective thickness for calculations
     * @return Thickness minus corrosion allowances
     */
    public Double getEffectiveThickness() {
        if (nominalThickness == null) return null;
        
        double thickness = nominalThickness;
        if (uniformMetalLoss != null) thickness -= uniformMetalLoss;
        if (futureCorrosionAllowance != null) thickness -= futureCorrosionAllowance;
        
        return Math.max(0, thickness);
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public AssessmentType getAssessmentType() { return assessmentType; }
    public void setAssessmentType(AssessmentType assessmentType) { this.assessmentType = assessmentType; }
    
    public AssessmentLevel getAssessmentLevel() { return assessmentLevel; }
    public void setAssessmentLevel(AssessmentLevel assessmentLevel) { this.assessmentLevel = assessmentLevel; }
    
    public AssessmentStatus getStatus() { return status; }
    public void setStatus(AssessmentStatus status) { this.status = status; }
    
    public String getComponentType() { return componentType; }
    public void setComponentType(String componentType) { this.componentType = componentType; }
    
    public String getComponentId() { return componentId; }
    public void setComponentId(String componentId) { this.componentId = componentId; }
    
    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }
    
    public Double getDesignPressure() { return designPressure; }
    public void setDesignPressure(Double designPressure) { this.designPressure = designPressure; }
    
    public Double getDesignTemperature() { return designTemperature; }
    public void setDesignTemperature(Double designTemperature) { this.designTemperature = designTemperature; }
    
    public Double getOperatingPressure() { return operatingPressure; }
    public void setOperatingPressure(Double operatingPressure) { this.operatingPressure = operatingPressure; }
    
    public Double getOperatingTemperature() { return operatingTemperature; }
    public void setOperatingTemperature(Double operatingTemperature) { this.operatingTemperature = operatingTemperature; }
    
    public Double getInsideDiameter() { return insideDiameter; }
    public void setInsideDiameter(Double insideDiameter) { this.insideDiameter = insideDiameter; }
    
    public Double getNominalThickness() { return nominalThickness; }
    public void setNominalThickness(Double nominalThickness) { this.nominalThickness = nominalThickness; }
    
    public Double getVesselHeight() { return vesselHeight; }
    public void setVesselHeight(Double vesselHeight) { this.vesselHeight = vesselHeight; }
    
    public Double getVesselLength() { return vesselLength; }
    public void setVesselLength(Double vesselLength) { this.vesselLength = vesselLength; }
    
    public Material getMaterial() { return material; }
    public void setMaterial(Material material) { this.material = material; }
    
    public Double getUniformMetalLoss() { return uniformMetalLoss; }
    public void setUniformMetalLoss(Double uniformMetalLoss) { this.uniformMetalLoss = uniformMetalLoss; }
    
    public Double getFutureCorrosionAllowance() { return futureCorrosionAllowance; }
    public void setFutureCorrosionAllowance(Double futureCorrosionAllowance) { this.futureCorrosionAllowance = futureCorrosionAllowance; }
    
    public Double getCorrosionRate() { return corrosionRate; }
    public void setCorrosionRate(Double corrosionRate) { this.corrosionRate = corrosionRate; }
    
    public Double getLongitudinalWeldEfficiency() { return longitudinalWeldEfficiency; }
    public void setLongitudinalWeldEfficiency(Double longitudinalWeldEfficiency) { this.longitudinalWeldEfficiency = longitudinalWeldEfficiency; }
    
    public Double getCircumferentialWeldEfficiency() { return circumferentialWeldEfficiency; }
    public void setCircumferentialWeldEfficiency(Double circumferentialWeldEfficiency) { this.circumferentialWeldEfficiency = circumferentialWeldEfficiency; }
    
    public Boolean getIsAcceptable() { return isAcceptable; }
    public void setIsAcceptable(Boolean isAcceptable) { this.isAcceptable = isAcceptable; }
    
    public Double getMawp() { return mawp; }
    public void setMawp(Double mawp) { this.mawp = mawp; }
    
    public Double getMawpReduced() { return mawpReduced; }
    public void setMawpReduced(Double mawpReduced) { this.mawpReduced = mawpReduced; }
    
    public Double getRemainingLife() { return remainingLife; }
    public void setRemainingLife(Double remainingLife) { this.remainingLife = remainingLife; }
    
    public Double getRsf() { return rsf; }
    public void setRsf(Double rsf) { this.rsf = rsf; }
    
    public String getAssessmentNotes() { return assessmentNotes; }
    public void setAssessmentNotes(String assessmentNotes) { this.assessmentNotes = assessmentNotes; }
    
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    public LocalDateTime getAssessedAt() { return assessedAt; }
    public void setAssessedAt(LocalDateTime assessedAt) { this.assessedAt = assessedAt; }
    
    public List<ThicknessReading> getThicknessReadings() { return thicknessReadings; }
    public void setThicknessReadings(List<ThicknessReading> thicknessReadings) { this.thicknessReadings = thicknessReadings; }
    
    public List<PittingData> getPittingData() { return pittingData; }
    public void setPittingData(List<PittingData> pittingData) { this.pittingData = pittingData; }
    
    // Enums
    public enum AssessmentType {
        PART_4_GENERAL_METAL_LOSS,
        PART_5_LOCAL_THIN_AREAS,
        PART_6_PITTING_CORROSION
    }
    
    public enum AssessmentLevel {
        LEVEL_1,
        LEVEL_2
    }
    
    public enum AssessmentStatus {
        DRAFT,
        IN_PROGRESS,
        COMPLETED,
        REVIEWED,
        APPROVED
    }
}
