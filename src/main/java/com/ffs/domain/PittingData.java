package com.ffs.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.time.LocalDateTime;

/**
 * Pitting data entity for storing pit measurements and characteristics
 * Used for Part 6 Pitting Corrosion assessments
 */
@Entity
@Table(name = "pitting_data", indexes = {
    @Index(name = "idx_pitting_assessment", columnList = "assessment_id"),
    @Index(name = "idx_pitting_couple", columnList = "pit_couple_id"),
    @Index(name = "idx_pitting_depth", columnList = "pit_depth")
})
public class PittingData {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assessment_id", nullable = false)
    private Assessment assessment;
    
    @NotNull
    @Column(name = "pit_couple_id", nullable = false)
    private Integer pitCoupleId; // Pit-couple identifier
    
    @NotNull
    @Column(name = "pit_number", nullable = false)
    private Integer pitNumber; // Pit number within the couple (1 or 2)
    
    @Positive
    @Column(name = "pit_depth", nullable = false)
    private Double pitDepth; // Pit depth (inches)
    
    @Positive
    @Column(name = "pit_diameter", nullable = false)
    private Double pitDiameter; // Pit diameter (inches)
    
    @Column(name = "pit_length")
    private Double pitLength; // For elongated pits (inches)
    
    @Column(name = "pit_width")
    private Double pitWidth; // For elongated pits (inches)
    
    @Column(name = "distance_between_pits")
    private Double distanceBetweenPits; // Distance to paired pit (inches)
    
    @Column(name = "angular_position")
    private Double angularPosition; // Angular position (degrees)
    
    @Column(name = "axial_position")
    private Double axialPosition; // Axial position from reference (inches)
    
    @Column(name = "circumferential_position")
    private Double circumferentialPosition; // Circumferential position (inches)
    
    @Enumerated(EnumType.STRING)
    @Column(name = "pit_shape")
    private PitShape pitShape;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "pit_orientation")
    private PitOrientation pitOrientation;
    
    @Column(name = "remaining_thickness")
    private Double remainingThickness; // Thickness at pit location (inches)
    
    @Column(name = "original_thickness")
    private Double originalThickness; // Original thickness at pit location (inches)
    
    @Column(name = "surface_roughness")
    private Double surfaceRoughness; // Surface roughness factor
    
    @Column(name = "pit_density")
    private Double pitDensity; // Pits per unit area
    
    @Enumerated(EnumType.STRING)
    @Column(name = "pitting_grade")
    private PittingGrade pittingGrade; // Based on API 579-1 Figure 6.6-6.13
    
    @Column(name = "is_isolated")
    private Boolean isIsolated = false; // Single isolated pit
    
    @Column(name = "is_clustered")
    private Boolean isClustered = false; // Part of pit cluster
    
    @Column(name = "cluster_id")
    private String clusterId; // Cluster identifier
    
    @Column(name = "measurement_method", length = 100)
    private String measurementMethod; // e.g., "Pit gauge", "Ultrasonic"
    
    @Column(name = "measurement_date")
    private LocalDateTime measurementDate;
    
    @Column(name = "inspector", length = 100)
    private String inspector;
    
    @Column(name = "notes", length = 500)
    private String notes;
    
    @Column(name = "is_excluded")
    private Boolean isExcluded = false;
    
    @Column(name = "exclusion_reason", length = 200)
    private String exclusionReason;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public PittingData() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public PittingData(Assessment assessment, Integer pitCoupleId, Integer pitNumber, 
                      Double pitDepth, Double pitDiameter) {
        this();
        this.assessment = assessment;
        this.pitCoupleId = pitCoupleId;
        this.pitNumber = pitNumber;
        this.pitDepth = pitDepth;
        this.pitDiameter = pitDiameter;
    }
    
    // Lifecycle callbacks
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    @PostLoad
    @PostPersist
    @PostUpdate
    public void calculateRemainingThickness() {
        if (originalThickness != null && pitDepth != null) {
            this.remainingThickness = originalThickness - pitDepth;
        }
    }
    
    // Business methods
    
    /**
     * Calculate remaining wall thickness ratio (Rwt)
     * @param futureCorrosionAllowance FCA to account for
     * @param effectiveThickness Effective thickness for assessment
     * @return Rwt ratio
     */
    public Double calculateRwt(double futureCorrosionAllowance, double effectiveThickness) {
        if (remainingThickness == null) return null;
        
        double adjustedThickness = remainingThickness - futureCorrosionAllowance;
        return adjustedThickness / effectiveThickness;
    }
    
    /**
     * Calculate pit volume (simplified cylindrical approximation)
     * @return Pit volume in cubic inches
     */
    public Double calculatePitVolume() {
        if (pitDepth == null || pitDiameter == null) return null;
        
        double radius = pitDiameter / 2.0;
        return Math.PI * radius * radius * pitDepth;
    }
    
    /**
     * Calculate pit aspect ratio (depth/diameter)
     * @return Aspect ratio
     */
    public Double calculateAspectRatio() {
        if (pitDepth == null || pitDiameter == null || pitDiameter == 0) return null;
        return pitDepth / pitDiameter;
    }
    
    /**
     * Check if pit is acceptable based on basic criteria
     * @param minThickness Minimum allowable thickness
     * @return true if pit meets basic acceptance criteria
     */
    public boolean isAcceptable(double minThickness) {
        if (remainingThickness == null) return false;
        return remainingThickness >= minThickness && remainingThickness >= 0.1; // 0.1" minimum
    }
    
    /**
     * Get pit severity classification
     * @return Severity level
     */
    public PitSeverity getSeverity() {
        if (pitDepth == null || originalThickness == null || originalThickness == 0) {
            return PitSeverity.UNKNOWN;
        }
        
        double depthRatio = pitDepth / originalThickness;
        
        if (depthRatio < 0.1) return PitSeverity.MINOR;
        if (depthRatio < 0.25) return PitSeverity.MODERATE;
        if (depthRatio < 0.5) return PitSeverity.SEVERE;
        return PitSeverity.CRITICAL;
    }
    
    /**
     * Check if this pit is part of a pit-couple with another pit
     * @param other Other pit data
     * @return true if they form a pit-couple
     */
    public boolean isPitCoupleWith(PittingData other) {
        return this.pitCoupleId.equals(other.pitCoupleId) && 
               !this.pitNumber.equals(other.pitNumber);
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Assessment getAssessment() { return assessment; }
    public void setAssessment(Assessment assessment) { this.assessment = assessment; }
    
    public Integer getPitCoupleId() { return pitCoupleId; }
    public void setPitCoupleId(Integer pitCoupleId) { this.pitCoupleId = pitCoupleId; }
    
    public Integer getPitNumber() { return pitNumber; }
    public void setPitNumber(Integer pitNumber) { this.pitNumber = pitNumber; }
    
    public Double getPitDepth() { return pitDepth; }
    public void setPitDepth(Double pitDepth) { this.pitDepth = pitDepth; }
    
    public Double getPitDiameter() { return pitDiameter; }
    public void setPitDiameter(Double pitDiameter) { this.pitDiameter = pitDiameter; }
    
    public Double getPitLength() { return pitLength; }
    public void setPitLength(Double pitLength) { this.pitLength = pitLength; }
    
    public Double getPitWidth() { return pitWidth; }
    public void setPitWidth(Double pitWidth) { this.pitWidth = pitWidth; }
    
    public Double getDistanceBetweenPits() { return distanceBetweenPits; }
    public void setDistanceBetweenPits(Double distanceBetweenPits) { this.distanceBetweenPits = distanceBetweenPits; }
    
    public Double getAngularPosition() { return angularPosition; }
    public void setAngularPosition(Double angularPosition) { this.angularPosition = angularPosition; }
    
    public Double getAxialPosition() { return axialPosition; }
    public void setAxialPosition(Double axialPosition) { this.axialPosition = axialPosition; }
    
    public Double getCircumferentialPosition() { return circumferentialPosition; }
    public void setCircumferentialPosition(Double circumferentialPosition) { this.circumferentialPosition = circumferentialPosition; }
    
    public PitShape getPitShape() { return pitShape; }
    public void setPitShape(PitShape pitShape) { this.pitShape = pitShape; }
    
    public PitOrientation getPitOrientation() { return pitOrientation; }
    public void setPitOrientation(PitOrientation pitOrientation) { this.pitOrientation = pitOrientation; }
    
    public Double getRemainingThickness() { return remainingThickness; }
    public void setRemainingThickness(Double remainingThickness) { this.remainingThickness = remainingThickness; }
    
    public Double getOriginalThickness() { return originalThickness; }
    public void setOriginalThickness(Double originalThickness) { this.originalThickness = originalThickness; }
    
    public Double getSurfaceRoughness() { return surfaceRoughness; }
    public void setSurfaceRoughness(Double surfaceRoughness) { this.surfaceRoughness = surfaceRoughness; }
    
    public Double getPitDensity() { return pitDensity; }
    public void setPitDensity(Double pitDensity) { this.pitDensity = pitDensity; }
    
    public PittingGrade getPittingGrade() { return pittingGrade; }
    public void setPittingGrade(PittingGrade pittingGrade) { this.pittingGrade = pittingGrade; }
    
    public Boolean getIsIsolated() { return isIsolated; }
    public void setIsIsolated(Boolean isIsolated) { this.isIsolated = isIsolated; }
    
    public Boolean getIsClustered() { return isClustered; }
    public void setIsClustered(Boolean isClustered) { this.isClustered = isClustered; }
    
    public String getClusterId() { return clusterId; }
    public void setClusterId(String clusterId) { this.clusterId = clusterId; }
    
    public String getMeasurementMethod() { return measurementMethod; }
    public void setMeasurementMethod(String measurementMethod) { this.measurementMethod = measurementMethod; }
    
    public LocalDateTime getMeasurementDate() { return measurementDate; }
    public void setMeasurementDate(LocalDateTime measurementDate) { this.measurementDate = measurementDate; }
    
    public String getInspector() { return inspector; }
    public void setInspector(String inspector) { this.inspector = inspector; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public Boolean getIsExcluded() { return isExcluded; }
    public void setIsExcluded(Boolean isExcluded) { this.isExcluded = isExcluded; }
    
    public String getExclusionReason() { return exclusionReason; }
    public void setExclusionReason(String exclusionReason) { this.exclusionReason = exclusionReason; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    // Enums
    public enum PitShape {
        CIRCULAR,
        ELLIPTICAL,
        IRREGULAR,
        ELONGATED
    }
    
    public enum PitOrientation {
        LONGITUDINAL,
        CIRCUMFERENTIAL,
        RANDOM
    }
    
    public enum PittingGrade {
        GRADE_1, // Minimal pitting
        GRADE_2, // Light pitting
        GRADE_3, // Moderate pitting
        GRADE_4, // Heavy pitting
        GRADE_5  // Severe pitting
    }
    
    public enum PitSeverity {
        MINOR,
        MODERATE,
        SEVERE,
        CRITICAL,
        UNKNOWN
    }
    
    @Override
    public String toString() {
        return String.format("PittingData{couple=%d, pit=%d, depth=%.3f, diameter=%.3f}", 
            pitCoupleId, pitNumber, pitDepth, pitDiameter);
    }
}
