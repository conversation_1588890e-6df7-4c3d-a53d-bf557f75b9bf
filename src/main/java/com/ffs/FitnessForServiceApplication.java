package com.ffs;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main Spring Boot Application for Fitness-for-Service Assessment System
 * 
 * This application provides comprehensive FFS assessments compliant with:
 * - API 579-1 / ASME FFS-1 standards
 * - Part 4: General Metal Loss
 * - Part 5: Local Thin Areas  
 * - Part 6: Pitting Corrosion
 * 
 * Features:
 * - Level 1 and Level 2 assessments
 * - Material database with temperature-dependent properties
 * - PDF report generation
 * - NDT data import (CSV/Excel)
 * - What-if scenario simulations
 * - User authentication and management
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableJpaRepositories
@EnableTransactionManagement
public class FitnessForServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(FitnessForServiceApplication.class, args);
    }
}
