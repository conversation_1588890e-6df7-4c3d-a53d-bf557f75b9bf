package com.ffs.service;

import com.ffs.domain.Assessment;
import com.ffs.domain.Material;
import com.ffs.domain.ThicknessReading;
import com.ffs.domain.PittingData;
import com.ffs.repository.AssessmentRepository;
import com.ffs.repository.MaterialRepository;
import com.ffs.controller.dto.AssessmentRequest;
import com.ffs.controller.AssessmentController.AssessmentStatistics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Optional;

/**
 * Service class for managing FFS assessments
 */
@Service
@Transactional
public class AssessmentService {
    
    @Autowired
    private AssessmentRepository assessmentRepository;
    
    @Autowired
    private MaterialRepository materialRepository;
    
    /**
     * Create a new assessment from request data
     */
    public Assessment createAssessment(AssessmentRequest request) {
        Assessment assessment = new Assessment();
        mapRequestToAssessment(request, assessment);
        return assessmentRepository.save(assessment);
    }
    
    /**
     * Update an existing assessment
     */
    public Assessment updateAssessment(Long id, AssessmentRequest request) {
        Optional<Assessment> existingOpt = assessmentRepository.findById(id);
        if (existingOpt.isEmpty()) {
            return null;
        }
        
        Assessment assessment = existingOpt.get();
        mapRequestToAssessment(request, assessment);
        return assessmentRepository.save(assessment);
    }
    
    /**
     * Get assessment by ID
     */
    @Transactional(readOnly = true)
    public Assessment getAssessmentById(Long id) {
        return assessmentRepository.findById(id).orElse(null);
    }
    
    /**
     * Get all assessments
     */
    @Transactional(readOnly = true)
    public List<Assessment> getAllAssessments() {
        return assessmentRepository.findAll();
    }
    
    /**
     * Delete an assessment
     */
    public boolean deleteAssessment(Long id) {
        if (assessmentRepository.existsById(id)) {
            assessmentRepository.deleteById(id);
            return true;
        }
        return false;
    }
    
    /**
     * Save assessment (for updates from calculation services)
     */
    public Assessment saveAssessment(Assessment assessment) {
        return assessmentRepository.save(assessment);
    }
    
    /**
     * Search assessments by criteria
     */
    @Transactional(readOnly = true)
    public List<Assessment> searchAssessments(String componentType, 
                                            Assessment.AssessmentType assessmentType,
                                            Assessment.AssessmentStatus status) {
        return assessmentRepository.findByCriteria(componentType, assessmentType, status);
    }
    
    /**
     * Get assessment statistics
     */
    @Transactional(readOnly = true)
    public AssessmentStatistics getStatistics() {
        long total = assessmentRepository.count();
        long completed = assessmentRepository.countByStatus(Assessment.AssessmentStatus.COMPLETED);
        long acceptable = assessmentRepository.countByIsAcceptable(true);
        long part4 = assessmentRepository.countByAssessmentType(Assessment.AssessmentType.PART_4_GENERAL_METAL_LOSS);
        long part5 = assessmentRepository.countByAssessmentType(Assessment.AssessmentType.PART_5_LOCAL_THIN_AREAS);
        long part6 = assessmentRepository.countByAssessmentType(Assessment.AssessmentType.PART_6_PITTING_CORROSION);
        
        return new AssessmentStatistics(total, completed, acceptable, part4, part5, part6);
    }
    
    /**
     * Get assessments by status
     */
    @Transactional(readOnly = true)
    public List<Assessment> getAssessmentsByStatus(Assessment.AssessmentStatus status) {
        return assessmentRepository.findByStatus(status);
    }
    
    /**
     * Get assessments requiring attention (completed but not acceptable)
     */
    @Transactional(readOnly = true)
    public List<Assessment> getAssessmentsRequiringAttention() {
        return assessmentRepository.findByStatusAndIsAcceptable(
            Assessment.AssessmentStatus.COMPLETED, false);
    }
    
    /**
     * Map request data to assessment entity
     */
    private void mapRequestToAssessment(AssessmentRequest request, Assessment assessment) {
        // Basic information
        assessment.setName(request.getName());
        assessment.setDescription(request.getDescription());
        assessment.setAssessmentType(request.getAssessmentType());
        assessment.setAssessmentLevel(request.getAssessmentLevel());
        
        // Component information
        assessment.setComponentType(request.getComponentType());
        assessment.setComponentId(request.getComponentId());
        assessment.setLocation(request.getLocation());
        
        // Design conditions
        assessment.setDesignPressure(request.getDesignPressure());
        assessment.setDesignTemperature(request.getDesignTemperature());
        assessment.setOperatingPressure(request.getOperatingPressure());
        assessment.setOperatingTemperature(request.getOperatingTemperature());
        
        // Geometry
        assessment.setInsideDiameter(request.getInsideDiameter());
        assessment.setNominalThickness(request.getNominalThickness());
        assessment.setVesselHeight(request.getVesselHeight());
        assessment.setVesselLength(request.getVesselLength());
        
        // Material
        if (request.getMaterialId() != null) {
            Optional<Material> materialOpt = materialRepository.findById(request.getMaterialId());
            materialOpt.ifPresent(assessment::setMaterial);
        }
        
        // Corrosion data
        assessment.setUniformMetalLoss(request.getUniformMetalLoss());
        assessment.setFutureCorrosionAllowance(request.getFutureCorrosionAllowance());
        assessment.setCorrosionRate(request.getCorrosionRate());
        
        // Weld efficiency
        assessment.setLongitudinalWeldEfficiency(request.getLongitudinalWeldEfficiency());
        assessment.setCircumferentialWeldEfficiency(request.getCircumferentialWeldEfficiency());
        
        // Notes
        assessment.setAssessmentNotes(request.getAssessmentNotes());
        
        // Thickness readings
        if (request.getThicknessReadings() != null) {
            assessment.getThicknessReadings().clear();
            for (AssessmentRequest.ThicknessReadingRequest readingRequest : request.getThicknessReadings()) {
                ThicknessReading reading = new ThicknessReading();
                reading.setAssessment(assessment);
                reading.setSequenceNumber(readingRequest.getSequenceNumber());
                reading.setLocationX(readingRequest.getLocationX());
                reading.setLocationY(readingRequest.getLocationY());
                reading.setLocationDescription(readingRequest.getLocationDescription());
                reading.setThickness(readingRequest.getThickness());
                reading.setOriginalThickness(readingRequest.getOriginalThickness());
                reading.setInspector(readingRequest.getInspector());
                reading.setNotes(readingRequest.getNotes());
                
                if (readingRequest.getMeasurementMethod() != null) {
                    try {
                        reading.setMeasurementMethod(
                            ThicknessReading.MeasurementMethod.valueOf(readingRequest.getMeasurementMethod()));
                    } catch (IllegalArgumentException e) {
                        // Default to ultrasonic if invalid method provided
                        reading.setMeasurementMethod(ThicknessReading.MeasurementMethod.ULTRASONIC_THICKNESS);
                    }
                }
                
                assessment.getThicknessReadings().add(reading);
            }
        }
        
        // Pitting data
        if (request.getPittingData() != null) {
            assessment.getPittingData().clear();
            for (AssessmentRequest.PittingDataRequest pittingRequest : request.getPittingData()) {
                PittingData pitting = new PittingData();
                pitting.setAssessment(assessment);
                pitting.setPitCoupleId(pittingRequest.getPitCoupleId());
                pitting.setPitNumber(pittingRequest.getPitNumber());
                pitting.setPitDepth(pittingRequest.getPitDepth());
                pitting.setPitDiameter(pittingRequest.getPitDiameter());
                pitting.setPitLength(pittingRequest.getPitLength());
                pitting.setPitWidth(pittingRequest.getPitWidth());
                pitting.setDistanceBetweenPits(pittingRequest.getDistanceBetweenPits());
                pitting.setAngularPosition(pittingRequest.getAngularPosition());
                pitting.setAxialPosition(pittingRequest.getAxialPosition());
                pitting.setRemainingThickness(pittingRequest.getRemainingThickness());
                pitting.setOriginalThickness(pittingRequest.getOriginalThickness());
                pitting.setMeasurementMethod(pittingRequest.getMeasurementMethod());
                pitting.setInspector(pittingRequest.getInspector());
                pitting.setNotes(pittingRequest.getNotes());
                
                // Handle enums with error handling
                if (pittingRequest.getPitShape() != null) {
                    try {
                        pitting.setPitShape(PittingData.PitShape.valueOf(pittingRequest.getPitShape()));
                    } catch (IllegalArgumentException e) {
                        pitting.setPitShape(PittingData.PitShape.CIRCULAR);
                    }
                }
                
                if (pittingRequest.getPitOrientation() != null) {
                    try {
                        pitting.setPitOrientation(PittingData.PitOrientation.valueOf(pittingRequest.getPitOrientation()));
                    } catch (IllegalArgumentException e) {
                        pitting.setPitOrientation(PittingData.PitOrientation.RANDOM);
                    }
                }
                
                if (pittingRequest.getPittingGrade() != null) {
                    try {
                        pitting.setPittingGrade(PittingData.PittingGrade.valueOf(pittingRequest.getPittingGrade()));
                    } catch (IllegalArgumentException e) {
                        pitting.setPittingGrade(PittingData.PittingGrade.GRADE_1);
                    }
                }
                
                assessment.getPittingData().add(pitting);
            }
        }
    }
}
