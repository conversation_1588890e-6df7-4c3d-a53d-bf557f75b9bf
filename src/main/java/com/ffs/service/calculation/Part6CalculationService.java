package com.ffs.service.calculation;

import com.ffs.domain.Assessment;
import com.ffs.domain.Material;
import com.ffs.domain.PittingData;
import com.ffs.service.calculation.dto.Part6Result;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Part 6 Pitting Corrosion Calculation Service
 * Implements API 579-1/ASME FFS-1 Part 6 assessment procedures
 */
@Service
public class Part6CalculationService {
    
    private static final double RSF_A = 0.9; // Remaining Strength Factor for Level 2
    private static final int PRECISION = 6;
    
    /**
     * Perform Level 1 Assessment for Pitting Corrosion
     * @param assessment Assessment data
     * @return Part 6 Level 1 results
     */
    public Part6Result performLevel1Assessment(Assessment assessment) {
        Part6Result result = new Part6Result();
        result.setAssessmentLevel(Assessment.AssessmentLevel.LEVEL_1);
        
        try {
            // Step 1: Validate component type and pitting criteria
            validateComponentType(assessment, result);
            if (!result.isComponentTypeValid()) {
                return result;
            }
            
            // Step 2: Get pitting data
            List<PittingData> validPits = getValidPittingData(assessment);
            if (validPits.isEmpty()) {
                result.addError("No valid pitting data found");
                return result;
            }
            
            // Step 3: Calculate pit depths and remaining thicknesses
            double maxPitDepth = calculateMaximumPitDepth(validPits);
            double minRemainingThickness = calculateMinimumRemainingThickness(validPits);
            
            result.setMaxPitDepth(round(maxPitDepth));
            result.setMinRemainingThickness(round(minRemainingThickness));
            
            // Step 4: Calculate pit density
            double pitDensity = calculatePitDensity(validPits, assessment);
            result.setPitDensity(round(pitDensity));
            
            // Step 5: Calculate required thickness
            double treq = calculateRequiredThickness(assessment);
            result.setTreq(round(treq));
            
            // Step 6: Calculate RSF for pitting
            double rsf = calculateRSFForPitting(minRemainingThickness, treq, assessment);
            result.setRsf(round(rsf));
            
            // Step 7: Calculate MAWP with pits
            double mawp = calculateMAWPWithPits(assessment, minRemainingThickness);
            result.setMawp(round(mawp));
            
            // Step 8: Check acceptance criteria
            boolean acceptable = checkAcceptanceCriteria(minRemainingThickness, treq, rsf, result);
            result.setAcceptable(acceptable);
            
            // Check MAWP against design pressure
            if (assessment.getDesignPressure() != null) {
                boolean mawpAcceptable = mawp >= assessment.getDesignPressure();
                result.setMawpAcceptable(mawpAcceptable);
                result.setAcceptable(acceptable && mawpAcceptable);
            }
            
            // Calculate remaining life if corrosion rate is provided
            if (assessment.getCorrosionRate() != null && assessment.getCorrosionRate() > 0) {
                double remainingLife = calculateRemainingLife(minRemainingThickness, treq, assessment.getCorrosionRate());
                result.setRemainingLife(round(remainingLife));
            }
            
        } catch (Exception e) {
            result.addError("Calculation error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * Perform Level 2 Assessment for Pitting Corrosion
     * @param assessment Assessment data
     * @return Part 6 Level 2 results
     */
    public Part6Result performLevel2Assessment(Assessment assessment) {
        Part6Result result = performLevel1Assessment(assessment);
        result.setAssessmentLevel(Assessment.AssessmentLevel.LEVEL_2);
        
        if (!result.isComponentTypeValid()) {
            return result;
        }
        
        try {
            // Level 2 uses pit-couple analysis and stress field calculations
            List<PittingData> validPits = getValidPittingData(assessment);
            
            // Calculate average RSF for all pit-couples
            double averageRSF = calculateAverageRSFForPitCouples(validPits, assessment);
            result.setAverageRsf(round(averageRSF));
            
            // Check Level 2 acceptance criteria
            boolean acceptableLevel2 = averageRSF >= RSF_A;
            result.setAcceptableLevel2(acceptableLevel2);
            
            if (!acceptableLevel2) {
                // Calculate reduced MAWP
                double mawpReduced = result.getMawp() * (averageRSF / RSF_A);
                result.setMawpReduced(round(mawpReduced));
            }
            
        } catch (Exception e) {
            result.addError("Level 2 calculation error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * Validate component type for Part 6 assessment
     */
    private void validateComponentType(Assessment assessment, Part6Result result) {
        try {
            if (assessment.getVesselHeight() != null && assessment.getInsideDiameter() != null) {
                double hOverD = assessment.getVesselHeight() / assessment.getInsideDiameter();
                boolean criteria1 = hOverD <= 3.0;
                boolean criteria2 = assessment.getVesselHeight() <= 1200.0; // 100 ft in inches
                
                result.setComponentType("Type A");
                result.setComponentTypeValid(criteria1 && criteria2);
                
                result.addNote(String.format("H/D = %.3f (≤ 3.0: %s)", hOverD, criteria1 ? "PASSED" : "FAILED"));
                result.addNote(String.format("H = %.1f in (≤ 1200 in: %s)", 
                    assessment.getVesselHeight(), criteria2 ? "PASSED" : "FAILED"));
            } else {
                result.setComponentType("Type A"); // Default for pitting assessment
                result.setComponentTypeValid(true);
            }
        } catch (Exception e) {
            result.setComponentTypeValid(false);
            result.addError("Component type validation error: " + e.getMessage());
        }
    }
    
    /**
     * Get valid pitting data for calculations
     */
    private List<PittingData> getValidPittingData(Assessment assessment) {
        return assessment.getPittingData().stream()
            .filter(pit -> !Boolean.TRUE.equals(pit.getIsExcluded()))
            .filter(pit -> pit.getPitDepth() != null && pit.getPitDepth() > 0)
            .collect(Collectors.toList());
    }
    
    /**
     * Calculate maximum pit depth
     * Formula: d_pit = t_nom - t_pit
     */
    private double calculateMaximumPitDepth(List<PittingData> pits) {
        return pits.stream()
            .mapToDouble(PittingData::getPitDepth)
            .max()
            .orElse(0.0);
    }
    
    /**
     * Calculate minimum remaining thickness at pit locations
     */
    private double calculateMinimumRemainingThickness(List<PittingData> pits) {
        return pits.stream()
            .filter(pit -> pit.getRemainingThickness() != null)
            .mapToDouble(PittingData::getRemainingThickness)
            .min()
            .orElse(0.0);
    }
    
    /**
     * Calculate pit density
     * Formula: D = N_pits / A
     */
    private double calculatePitDensity(List<PittingData> pits, Assessment assessment) {
        // Simplified calculation - would need actual surface area evaluation
        double surfaceArea = Math.PI * assessment.getInsideDiameter() * assessment.getVesselHeight();
        return pits.size() / surfaceArea;
    }
    
    /**
     * Calculate required thickness from design code
     * Formula: t_req = (P * R) / (S * E - 0.6 * P)
     */
    private double calculateRequiredThickness(Assessment assessment) {
        double pressure = assessment.getDesignPressure();
        double radius = assessment.getInsideDiameter() / 2.0;
        double allowableStress = getAllowableStress(assessment);
        double weldEfficiency = assessment.getCircumferentialWeldEfficiency();
        
        return (pressure * radius) / (allowableStress * weldEfficiency - 0.6 * pressure);
    }
    
    /**
     * Calculate RSF for pitting
     * Formula: RSF = t_pit / t_req
     */
    private double calculateRSFForPitting(double remainingThickness, double requiredThickness, Assessment assessment) {
        double fca = assessment.getFutureCorrosionAllowance() != null ? 
                    assessment.getFutureCorrosionAllowance() : 0.0;
        
        double effectiveThickness = remainingThickness - fca;
        
        if (requiredThickness <= 0) return 0.0;
        return effectiveThickness / requiredThickness;
    }
    
    /**
     * Calculate MAWP with pits
     * Formula: MAWP = (2 * S * E * (t_pit - CA)) / R
     */
    private double calculateMAWPWithPits(Assessment assessment, double remainingThickness) {
        double radius = assessment.getInsideDiameter() / 2.0;
        double allowableStress = getAllowableStress(assessment);
        double weldEfficiency = assessment.getCircumferentialWeldEfficiency();
        double fca = assessment.getFutureCorrosionAllowance() != null ? 
                    assessment.getFutureCorrosionAllowance() : 0.0;
        
        double effectiveThickness = remainingThickness - fca;
        return (2 * allowableStress * weldEfficiency * effectiveThickness) / radius;
    }
    
    /**
     * Check acceptance criteria for pitting
     * Criteria: t_pit ≥ t_req, or RSF ≥ 0.9
     */
    private boolean checkAcceptanceCriteria(double remainingThickness, double requiredThickness, 
                                          double rsf, Part6Result result) {
        double fca = 0.0; // Simplified for now
        double effectiveThickness = remainingThickness - fca;
        
        boolean criteria1 = effectiveThickness >= requiredThickness;
        boolean criteria2 = rsf >= RSF_A;
        
        result.addNote(String.format("t_pit ≥ t_req: %s (%.6f ≥ %.6f)", 
            criteria1 ? "PASSED" : "FAILED", effectiveThickness, requiredThickness));
        result.addNote(String.format("RSF ≥ 0.9: %s (%.6f ≥ 0.9)", 
            criteria2 ? "PASSED" : "FAILED", rsf));
        
        return criteria1 || criteria2;
    }
    
    /**
     * Calculate average RSF for pit-couples (Level 2)
     */
    private double calculateAverageRSFForPitCouples(List<PittingData> pits, Assessment assessment) {
        // Group pits by pit-couple ID and calculate RSF for each couple
        // This is a simplified implementation
        return pits.stream()
            .mapToDouble(pit -> {
                double remainingThickness = pit.getRemainingThickness() != null ? 
                    pit.getRemainingThickness() : 0.0;
                double requiredThickness = calculateRequiredThickness(assessment);
                return calculateRSFForPitting(remainingThickness, requiredThickness, assessment);
            })
            .average()
            .orElse(0.0);
    }
    
    /**
     * Calculate remaining life based on corrosion rate
     */
    private double calculateRemainingLife(double currentThickness, double requiredThickness, double corrosionRate) {
        double availableThickness = currentThickness - requiredThickness;
        return Math.max(0, availableThickness / corrosionRate);
    }
    
    /**
     * Get allowable stress for material at design temperature
     */
    private double getAllowableStress(Assessment assessment) {
        Material material = assessment.getMaterial();
        if (material != null && assessment.getDesignTemperature() != null) {
            Double stress = material.getAllowableStress(assessment.getDesignTemperature());
            if (stress != null) {
                return stress;
            }
        }
        // Default allowable stress if not found in material database
        return 17500.0; // psi for typical carbon steel
    }
    
    /**
     * Round value to specified precision
     */
    private double round(double value) {
        return BigDecimal.valueOf(value)
            .setScale(PRECISION, RoundingMode.HALF_UP)
            .doubleValue();
    }
}
