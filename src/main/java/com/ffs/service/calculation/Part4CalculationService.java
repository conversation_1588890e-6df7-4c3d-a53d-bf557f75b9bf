package com.ffs.service.calculation;

import com.ffs.domain.Assessment;
import com.ffs.domain.Material;
import com.ffs.domain.ThicknessReading;
import com.ffs.service.calculation.dto.Part4Result;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Part 4 General Metal Loss Calculation Service
 * Implements API 579-1/ASME FFS-1 Part 4 assessment procedures
 */
@Service
public class Part4CalculationService {
    
    private static final double RSF_A = 0.9; // Remaining Strength Factor for Level 2
    private static final int PRECISION = 6;
    
    /**
     * Perform Level 1 Assessment for General Metal Loss
     * @param assessment Assessment data
     * @return Part 4 Level 1 results
     */
    public Part4Result performLevel1Assessment(Assessment assessment) {
        Part4Result result = new Part4Result();
        result.setAssessmentLevel(Assessment.AssessmentLevel.LEVEL_1);
        
        try {
            // Step 1: Validate component type
            validateComponentType(assessment, result);
            if (!result.isComponentTypeValid()) {
                return result;
            }
            
            // Step 2: Get thickness readings and determine tmm & tam
            List<ThicknessReading> validReadings = getValidThicknessReadings(assessment);
            if (validReadings.isEmpty()) {
                result.addError("No valid thickness readings found");
                return result;
            }
            
            double tmm = calculateMinimumMeasuredThickness(validReadings);
            double tam = calculateAverageMeasuredThickness(validReadings);
            
            result.setTmm(round(tmm));
            result.setTam(round(tam));
            
            // Step 3: Check if point thickness method can be used
            boolean usePointThickness = tmm >= 0.9 * tam;
            result.setUsePointThicknessMethod(usePointThickness);
            
            if (!usePointThickness) {
                result.addNote("Thickness profiles method required (tmm < 0.9 * tam)");
                return performThicknessProfileMethod(assessment, result);
            }
            
            // Step 4: Calculate minimum required thickness
            double tcmin = calculateMinimumRequiredThickness(assessment);
            result.setTcmin(round(tcmin));
            
            // Step 5: Check acceptance criteria
            double fca = assessment.getFutureCorrosionAllowance() != null ? 
                        assessment.getFutureCorrosionAllowance() : 0.0;
            
            boolean acceptable = (tam - fca) >= tcmin;
            result.setAcceptable(acceptable);
            
            // Calculate MAWP based on average thickness
            double mawp = calculateMAWP(assessment, tam - fca);
            result.setMawp(round(mawp));
            
            // Check MAWP against design pressure
            if (assessment.getDesignPressure() != null) {
                boolean mawpAcceptable = mawp >= assessment.getDesignPressure();
                result.setMawpAcceptable(mawpAcceptable);
                
                if (!acceptable && !mawpAcceptable) {
                    result.addNote("Level 1 Assessment Criteria Not Satisfied");
                }
            }
            
            // Calculate remaining life if corrosion rate is provided
            if (assessment.getCorrosionRate() != null && assessment.getCorrosionRate() > 0) {
                double remainingLife = calculateRemainingLife(tam, fca, tcmin, assessment.getCorrosionRate());
                result.setRemainingLife(round(remainingLife));
            }
            
        } catch (Exception e) {
            result.addError("Calculation error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * Perform Level 2 Assessment for General Metal Loss
     * @param assessment Assessment data
     * @return Part 4 Level 2 results
     */
    public Part4Result performLevel2Assessment(Assessment assessment) {
        Part4Result result = performLevel1Assessment(assessment);
        result.setAssessmentLevel(Assessment.AssessmentLevel.LEVEL_2);
        
        if (!result.isComponentTypeValid()) {
            return result;
        }
        
        try {
            // Level 2 uses RSFa = 0.9
            double tcminLevel2 = result.getTcmin() * RSF_A;
            result.setTcminLevel2(round(tcminLevel2));
            
            double fca = assessment.getFutureCorrosionAllowance() != null ? 
                        assessment.getFutureCorrosionAllowance() : 0.0;
            
            // Check Level 2 acceptance criteria
            boolean acceptableLevel2 = (result.getTam() - fca) >= tcminLevel2;
            result.setAcceptableLevel2(acceptableLevel2);
            
            // Calculate Level 2 MAWP
            double mawpLevel2 = calculateMAWP(assessment, result.getTam() - fca) / RSF_A;
            result.setMawpLevel2(round(mawpLevel2));
            
            // Check minimum measured thickness criterion
            double tmin = tcminLevel2;
            double tlim = Math.max(0.2 * assessment.getNominalThickness(), 2.5 / 1000.0); // 2.5mm in inches
            double minThicknessLimit = Math.max(0.5 * tmin, tlim);
            
            boolean tmmAcceptable = (result.getTmm() - fca) >= minThicknessLimit;
            result.setTmmAcceptableLevel2(tmmAcceptable);
            
            result.setAcceptable(acceptableLevel2 && tmmAcceptable);
            
            // Calculate Level 2 remaining life
            if (assessment.getCorrosionRate() != null && assessment.getCorrosionRate() > 0) {
                double remainingLifeLevel2 = calculateRemainingLife(
                    result.getTam(), fca, tcminLevel2, assessment.getCorrosionRate());
                result.setRemainingLifeLevel2(round(remainingLifeLevel2));
            }
            
        } catch (Exception e) {
            result.addError("Level 2 calculation error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * Validate component type according to Figure 4.3
     */
    private void validateComponentType(Assessment assessment, Part4Result result) {
        try {
            // Type A component criteria
            if (assessment.getVesselHeight() != null && assessment.getInsideDiameter() != null) {
                double hOverD = assessment.getVesselHeight() / assessment.getInsideDiameter();
                boolean criteria1 = hOverD <= 3.0;
                boolean criteria2 = assessment.getVesselHeight() <= 1200.0; // 100 ft in inches
                
                result.setComponentType("Type A");
                result.setComponentTypeValid(criteria1 && criteria2);
                
                result.addNote(String.format("H/D = %.3f (≤ 3.0: %s)", hOverD, criteria1 ? "PASSED" : "FAILED"));
                result.addNote(String.format("H = %.1f in (≤ 1200 in: %s)", 
                    assessment.getVesselHeight(), criteria2 ? "PASSED" : "FAILED"));
            }
            // Type B component criteria (saddle supported)
            else if (assessment.getVesselLength() != null && assessment.getInsideDiameter() != null) {
                // Assume saddle support distance from vessel length
                double lssOverD = assessment.getVesselLength() / assessment.getInsideDiameter();
                boolean criteria1 = lssOverD <= 2.5;
                boolean criteria2 = assessment.getInsideDiameter() <= 3660.0; // 10 ft in inches
                
                result.setComponentType("Type B");
                result.setComponentTypeValid(criteria1 && criteria2);
                
                result.addNote(String.format("Lss/D = %.3f (≤ 2.5: %s)", lssOverD, criteria1 ? "PASSED" : "FAILED"));
                result.addNote(String.format("D = %.1f in (≤ 3660 in: %s)", 
                    assessment.getInsideDiameter(), criteria2 ? "PASSED" : "FAILED"));
            } else {
                result.setComponentType("Unknown");
                result.setComponentTypeValid(false);
                result.addError("Insufficient geometry data for component classification");
            }
        } catch (Exception e) {
            result.setComponentTypeValid(false);
            result.addError("Component type validation error: " + e.getMessage());
        }
    }
    
    /**
     * Get valid thickness readings for calculations
     */
    private List<ThicknessReading> getValidThicknessReadings(Assessment assessment) {
        return assessment.getThicknessReadings().stream()
            .filter(ThicknessReading::isValidForCalculations)
            .collect(Collectors.toList());
    }
    
    /**
     * Calculate minimum measured thickness (tmm)
     */
    private double calculateMinimumMeasuredThickness(List<ThicknessReading> readings) {
        return readings.stream()
            .mapToDouble(ThicknessReading::getThickness)
            .min()
            .orElse(0.0);
    }
    
    /**
     * Calculate average measured thickness (tam)
     */
    private double calculateAverageMeasuredThickness(List<ThicknessReading> readings) {
        return readings.stream()
            .mapToDouble(ThicknessReading::getThickness)
            .average()
            .orElse(0.0);
    }
    
    /**
     * Calculate minimum required thickness using ASME formulas
     * Formula: t_req = (P * R) / (S * E - 0.6 * P)
     */
    private double calculateMinimumRequiredThickness(Assessment assessment) {
        double pressure = assessment.getDesignPressure();
        double radius = assessment.getInsideDiameter() / 2.0;
        double allowableStress = getAllowableStress(assessment);
        double weldEfficiency = assessment.getCircumferentialWeldEfficiency();

        // ASME Section VIII Div 1 formula: t_req = (P * R) / (S * E - 0.6 * P)
        return (pressure * radius) / (allowableStress * weldEfficiency - 0.6 * pressure);
    }

    /**
     * Calculate MAWP using remaining thickness
     * Formula: MAWP = (2 * S * E * (t_ml - CA)) / R
     */
    private double calculateMAWP(Assessment assessment, double thickness) {
        double radius = assessment.getInsideDiameter() / 2.0;
        double allowableStress = getAllowableStress(assessment);
        double weldEfficiency = assessment.getCircumferentialWeldEfficiency();

        // For cylindrical shells under internal pressure
        // MAWP = (2 * S * E * t) / R (simplified for thin wall)
        // More accurate: MAWP = (S * E * t) / (R + 0.6 * t)
        return (allowableStress * weldEfficiency * thickness) / (radius + 0.6 * thickness);
    }

    /**
     * Calculate Remaining Strength Factor (RSF)
     * Formula: RSF = MA_measured / MA_required
     */
    private double calculateRSF(double measuredThickness, double requiredThickness) {
        if (requiredThickness <= 0) return 0.0;
        return measuredThickness / requiredThickness;
    }

    /**
     * Calculate measured remaining thickness
     * Formula: t_ml = t_nom - (t_nom - t_meas) - CA
     */
    private double calculateMeasuredRemainingThickness(double nominalThickness,
                                                     double measuredThickness,
                                                     double corrosionAllowance) {
        return measuredThickness - corrosionAllowance;
    }
    
    /**
     * Get allowable stress for material at design temperature
     */
    private double getAllowableStress(Assessment assessment) {
        Material material = assessment.getMaterial();
        if (material != null && assessment.getDesignTemperature() != null) {
            Double stress = material.getAllowableStress(assessment.getDesignTemperature());
            if (stress != null) {
                return stress;
            }
        }
        // Default allowable stress if not found in material database
        return 17500.0; // psi for typical carbon steel
    }
    
    /**
     * Calculate remaining life based on corrosion rate
     */
    private double calculateRemainingLife(double currentThickness, double fca, 
                                        double minimumThickness, double corrosionRate) {
        double availableThickness = currentThickness - fca - minimumThickness;
        return Math.max(0, availableThickness / corrosionRate);
    }
    
    /**
     * Perform thickness profile method for complex geometries
     */
    private Part4Result performThicknessProfileMethod(Assessment assessment, Part4Result result) {
        // This would implement the Critical Thickness Profile (CTP) analysis
        // For now, return with note that this method is required
        result.addNote("Thickness Profile Method required - not implemented in this version");
        result.setAcceptable(false);
        return result;
    }
    
    /**
     * Round value to specified precision
     */
    private double round(double value) {
        return BigDecimal.valueOf(value)
            .setScale(PRECISION, RoundingMode.HALF_UP)
            .doubleValue();
    }
}
