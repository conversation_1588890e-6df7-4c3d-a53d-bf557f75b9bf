package com.ffs.service.calculation.dto;

import com.ffs.domain.Assessment;
import java.util.ArrayList;
import java.util.List;

/**
 * Data Transfer Object for Part 5 Local Thin Areas calculation results
 */
public class Part5Result {
    
    private Assessment.AssessmentLevel assessmentLevel;
    private String componentType;
    private boolean componentTypeValid;
    private boolean limitingCriteriaMet;
    
    // Thickness measurements
    private double tmm; // Minimum measured thickness
    private double tam; // Average measured thickness
    private double treq; // Required thickness
    
    // LTA dimensions
    private double longitudinalExtent; // s
    private double circumferentialExtent; // c
    private double assessmentLength; // L = sqrt(R*t)
    
    // Level 1 results
    private double mawp; // Maximum allowable working pressure
    private double rsf; // Remaining Strength Factor
    private boolean acceptable;
    private boolean mawpAcceptable;
    private double remainingLife; // years
    
    // Level 2 results
    private boolean acceptableLevel2;
    private double mawpReduced; // Reduced MAWP if RSF < RSFa
    private double remainingLifeLevel2; // years
    
    // Calculation details
    private List<String> notes;
    private List<String> errors;
    private List<CalculationStep> calculationSteps;
    
    public Part5Result() {
        this.notes = new ArrayList<>();
        this.errors = new ArrayList<>();
        this.calculationSteps = new ArrayList<>();
    }
    
    // Helper methods
    public void addNote(String note) {
        this.notes.add(note);
    }
    
    public void addError(String error) {
        this.errors.add(error);
    }
    
    public void addCalculationStep(String description, String formula, double result, String unit) {
        this.calculationSteps.add(new CalculationStep(description, formula, result, unit));
    }
    
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    public boolean isLevel1Acceptable() {
        return acceptable && !hasErrors();
    }
    
    public boolean isLevel2Acceptable() {
        return acceptableLevel2 && !hasErrors();
    }
    
    /**
     * Get the final assessment result
     */
    public String getAssessmentResult() {
        if (hasErrors()) {
            return "CALCULATION ERROR";
        }
        
        if (!limitingCriteriaMet) {
            return "LEVEL 2 ASSESSMENT REQUIRED";
        }
        
        if (assessmentLevel == Assessment.AssessmentLevel.LEVEL_1) {
            return acceptable ? "ACCEPTABLE (Level 1)" : "NOT ACCEPTABLE (Level 1)";
        } else {
            return isLevel2Acceptable() ? "ACCEPTABLE (Level 2)" : "NOT ACCEPTABLE (Level 2)";
        }
    }
    
    /**
     * Get recommended action based on results
     */
    public String getRecommendedAction() {
        if (hasErrors()) {
            return "Review input data and recalculate";
        }
        
        if (!limitingCriteriaMet) {
            return "Perform Level 2 assessment with detailed RSF calculations";
        }
        
        if (assessmentLevel == Assessment.AssessmentLevel.LEVEL_1) {
            if (acceptable) {
                return "LTA acceptable for continued operation";
            } else {
                return "Perform Level 2 assessment or consider repair/replacement";
            }
        } else {
            if (isLevel2Acceptable()) {
                return "LTA acceptable for continued operation";
            } else {
                return "LTA requires repair, replacement, or pressure reduction";
            }
        }
    }
    
    // Getters and Setters
    public Assessment.AssessmentLevel getAssessmentLevel() { return assessmentLevel; }
    public void setAssessmentLevel(Assessment.AssessmentLevel assessmentLevel) { this.assessmentLevel = assessmentLevel; }
    
    public String getComponentType() { return componentType; }
    public void setComponentType(String componentType) { this.componentType = componentType; }
    
    public boolean isComponentTypeValid() { return componentTypeValid; }
    public void setComponentTypeValid(boolean componentTypeValid) { this.componentTypeValid = componentTypeValid; }
    
    public boolean isLimitingCriteriaMet() { return limitingCriteriaMet; }
    public void setLimitingCriteriaMet(boolean limitingCriteriaMet) { this.limitingCriteriaMet = limitingCriteriaMet; }
    
    public double getTmm() { return tmm; }
    public void setTmm(double tmm) { this.tmm = tmm; }
    
    public double getTam() { return tam; }
    public void setTam(double tam) { this.tam = tam; }
    
    public double getTreq() { return treq; }
    public void setTreq(double treq) { this.treq = treq; }
    
    public double getLongitudinalExtent() { return longitudinalExtent; }
    public void setLongitudinalExtent(double longitudinalExtent) { this.longitudinalExtent = longitudinalExtent; }
    
    public double getCircumferentialExtent() { return circumferentialExtent; }
    public void setCircumferentialExtent(double circumferentialExtent) { this.circumferentialExtent = circumferentialExtent; }
    
    public double getAssessmentLength() { return assessmentLength; }
    public void setAssessmentLength(double assessmentLength) { this.assessmentLength = assessmentLength; }
    
    public double getMawp() { return mawp; }
    public void setMawp(double mawp) { this.mawp = mawp; }
    
    public double getRsf() { return rsf; }
    public void setRsf(double rsf) { this.rsf = rsf; }
    
    public boolean isAcceptable() { return acceptable; }
    public void setAcceptable(boolean acceptable) { this.acceptable = acceptable; }
    
    public boolean isMawpAcceptable() { return mawpAcceptable; }
    public void setMawpAcceptable(boolean mawpAcceptable) { this.mawpAcceptable = mawpAcceptable; }
    
    public double getRemainingLife() { return remainingLife; }
    public void setRemainingLife(double remainingLife) { this.remainingLife = remainingLife; }
    
    public boolean isAcceptableLevel2() { return acceptableLevel2; }
    public void setAcceptableLevel2(boolean acceptableLevel2) { this.acceptableLevel2 = acceptableLevel2; }
    
    public double getMawpReduced() { return mawpReduced; }
    public void setMawpReduced(double mawpReduced) { this.mawpReduced = mawpReduced; }
    
    public double getRemainingLifeLevel2() { return remainingLifeLevel2; }
    public void setRemainingLifeLevel2(double remainingLifeLevel2) { this.remainingLifeLevel2 = remainingLifeLevel2; }
    
    public List<String> getNotes() { return notes; }
    public void setNotes(List<String> notes) { this.notes = notes; }
    
    public List<String> getErrors() { return errors; }
    public void setErrors(List<String> errors) { this.errors = errors; }
    
    public List<CalculationStep> getCalculationSteps() { return calculationSteps; }
    public void setCalculationSteps(List<CalculationStep> calculationSteps) { this.calculationSteps = calculationSteps; }
    
    /**
     * Inner class for calculation step details
     */
    public static class CalculationStep {
        private String description;
        private String formula;
        private double result;
        private String unit;
        
        public CalculationStep(String description, String formula, double result, String unit) {
            this.description = description;
            this.formula = formula;
            this.result = result;
            this.unit = unit;
        }
        
        // Getters and Setters
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public String getFormula() { return formula; }
        public void setFormula(String formula) { this.formula = formula; }
        
        public double getResult() { return result; }
        public void setResult(double result) { this.result = result; }
        
        public String getUnit() { return unit; }
        public void setUnit(String unit) { this.unit = unit; }
        
        @Override
        public String toString() {
            return String.format("%s: %s = %.6f %s", description, formula, result, unit);
        }
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("Part 5 Assessment Results\n");
        sb.append("========================\n");
        sb.append(String.format("Assessment Level: %s\n", assessmentLevel));
        sb.append(String.format("Component Type: %s (Valid: %s)\n", componentType, componentTypeValid));
        sb.append(String.format("Limiting Criteria Met: %s\n", limitingCriteriaMet));
        sb.append(String.format("tmm: %.6f in\n", tmm));
        sb.append(String.format("tam: %.6f in\n", tam));
        sb.append(String.format("treq: %.6f in\n", treq));
        sb.append(String.format("RSF: %.6f\n", rsf));
        sb.append(String.format("MAWP: %.6f psi\n", mawp));
        sb.append(String.format("Result: %s\n", getAssessmentResult()));
        
        if (!notes.isEmpty()) {
            sb.append("\nNotes:\n");
            notes.forEach(note -> sb.append("- ").append(note).append("\n"));
        }
        
        if (!errors.isEmpty()) {
            sb.append("\nErrors:\n");
            errors.forEach(error -> sb.append("- ").append(error).append("\n"));
        }
        
        return sb.toString();
    }
}
