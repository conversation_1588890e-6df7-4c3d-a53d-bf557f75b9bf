package com.ffs.service.calculation.dto;

import com.ffs.domain.Assessment;
import java.util.ArrayList;
import java.util.List;

/**
 * Data Transfer Object for Part 6 Pitting Corrosion calculation results
 */
public class Part6Result {
    
    private Assessment.AssessmentLevel assessmentLevel;
    private String componentType;
    private boolean componentTypeValid;
    
    // Pitting measurements
    private double maxPitDepth; // Maximum pit depth
    private double minRemainingThickness; // Minimum remaining thickness at pits
    private double pitDensity; // Number of pits per unit area
    private double treq; // Required thickness
    
    // Level 1 results
    private double mawp; // Maximum allowable working pressure with pits
    private double rsf; // Remaining Strength Factor for pitting
    private boolean acceptable;
    private boolean mawpAcceptable;
    private double remainingLife; // years
    
    // Level 2 results
    private double averageRsf; // Average RSF for all pit-couples
    private boolean acceptableLevel2;
    private double mawpReduced; // Reduced MAWP if RSF < RSFa
    private double remainingLifeLevel2; // years
    
    // Pitting analysis details
    private int totalPits;
    private int pitCouples;
    private String pittingGrade; // Based on API 579-1 charts
    private double pitSpacingRatio; // s/t ratio
    
    // Calculation details
    private List<String> notes;
    private List<String> errors;
    private List<CalculationStep> calculationSteps;
    
    public Part6Result() {
        this.notes = new ArrayList<>();
        this.errors = new ArrayList<>();
        this.calculationSteps = new ArrayList<>();
    }
    
    // Helper methods
    public void addNote(String note) {
        this.notes.add(note);
    }
    
    public void addError(String error) {
        this.errors.add(error);
    }
    
    public void addCalculationStep(String description, String formula, double result, String unit) {
        this.calculationSteps.add(new CalculationStep(description, formula, result, unit));
    }
    
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    public boolean isLevel1Acceptable() {
        return acceptable && !hasErrors();
    }
    
    public boolean isLevel2Acceptable() {
        return acceptableLevel2 && !hasErrors();
    }
    
    /**
     * Get the final assessment result
     */
    public String getAssessmentResult() {
        if (hasErrors()) {
            return "CALCULATION ERROR";
        }
        
        if (assessmentLevel == Assessment.AssessmentLevel.LEVEL_1) {
            return acceptable ? "ACCEPTABLE (Level 1)" : "NOT ACCEPTABLE (Level 1)";
        } else {
            return isLevel2Acceptable() ? "ACCEPTABLE (Level 2)" : "NOT ACCEPTABLE (Level 2)";
        }
    }
    
    /**
     * Get recommended action based on results
     */
    public String getRecommendedAction() {
        if (hasErrors()) {
            return "Review input data and recalculate";
        }
        
        if (assessmentLevel == Assessment.AssessmentLevel.LEVEL_1) {
            if (acceptable) {
                return "Pitting acceptable for continued operation";
            } else {
                return "Perform Level 2 assessment or consider repair/replacement";
            }
        } else {
            if (isLevel2Acceptable()) {
                return "Pitting acceptable for continued operation";
            } else {
                return "Pitting requires repair, replacement, or pressure reduction";
            }
        }
    }
    
    /**
     * Get pitting severity assessment
     */
    public String getPittingSeverity() {
        if (maxPitDepth == 0) return "No significant pitting";
        
        // Simplified severity classification
        if (rsf >= 0.9) return "Minor";
        if (rsf >= 0.7) return "Moderate";
        if (rsf >= 0.5) return "Severe";
        return "Critical";
    }
    
    // Getters and Setters
    public Assessment.AssessmentLevel getAssessmentLevel() { return assessmentLevel; }
    public void setAssessmentLevel(Assessment.AssessmentLevel assessmentLevel) { this.assessmentLevel = assessmentLevel; }
    
    public String getComponentType() { return componentType; }
    public void setComponentType(String componentType) { this.componentType = componentType; }
    
    public boolean isComponentTypeValid() { return componentTypeValid; }
    public void setComponentTypeValid(boolean componentTypeValid) { this.componentTypeValid = componentTypeValid; }
    
    public double getMaxPitDepth() { return maxPitDepth; }
    public void setMaxPitDepth(double maxPitDepth) { this.maxPitDepth = maxPitDepth; }
    
    public double getMinRemainingThickness() { return minRemainingThickness; }
    public void setMinRemainingThickness(double minRemainingThickness) { this.minRemainingThickness = minRemainingThickness; }
    
    public double getPitDensity() { return pitDensity; }
    public void setPitDensity(double pitDensity) { this.pitDensity = pitDensity; }
    
    public double getTreq() { return treq; }
    public void setTreq(double treq) { this.treq = treq; }
    
    public double getMawp() { return mawp; }
    public void setMawp(double mawp) { this.mawp = mawp; }
    
    public double getRsf() { return rsf; }
    public void setRsf(double rsf) { this.rsf = rsf; }
    
    public boolean isAcceptable() { return acceptable; }
    public void setAcceptable(boolean acceptable) { this.acceptable = acceptable; }
    
    public boolean isMawpAcceptable() { return mawpAcceptable; }
    public void setMawpAcceptable(boolean mawpAcceptable) { this.mawpAcceptable = mawpAcceptable; }
    
    public double getRemainingLife() { return remainingLife; }
    public void setRemainingLife(double remainingLife) { this.remainingLife = remainingLife; }
    
    public double getAverageRsf() { return averageRsf; }
    public void setAverageRsf(double averageRsf) { this.averageRsf = averageRsf; }
    
    public boolean isAcceptableLevel2() { return acceptableLevel2; }
    public void setAcceptableLevel2(boolean acceptableLevel2) { this.acceptableLevel2 = acceptableLevel2; }
    
    public double getMawpReduced() { return mawpReduced; }
    public void setMawpReduced(double mawpReduced) { this.mawpReduced = mawpReduced; }
    
    public double getRemainingLifeLevel2() { return remainingLifeLevel2; }
    public void setRemainingLifeLevel2(double remainingLifeLevel2) { this.remainingLifeLevel2 = remainingLifeLevel2; }
    
    public int getTotalPits() { return totalPits; }
    public void setTotalPits(int totalPits) { this.totalPits = totalPits; }
    
    public int getPitCouples() { return pitCouples; }
    public void setPitCouples(int pitCouples) { this.pitCouples = pitCouples; }
    
    public String getPittingGrade() { return pittingGrade; }
    public void setPittingGrade(String pittingGrade) { this.pittingGrade = pittingGrade; }
    
    public double getPitSpacingRatio() { return pitSpacingRatio; }
    public void setPitSpacingRatio(double pitSpacingRatio) { this.pitSpacingRatio = pitSpacingRatio; }
    
    public List<String> getNotes() { return notes; }
    public void setNotes(List<String> notes) { this.notes = notes; }
    
    public List<String> getErrors() { return errors; }
    public void setErrors(List<String> errors) { this.errors = errors; }
    
    public List<CalculationStep> getCalculationSteps() { return calculationSteps; }
    public void setCalculationSteps(List<CalculationStep> calculationSteps) { this.calculationSteps = calculationSteps; }
    
    /**
     * Inner class for calculation step details
     */
    public static class CalculationStep {
        private String description;
        private String formula;
        private double result;
        private String unit;
        
        public CalculationStep(String description, String formula, double result, String unit) {
            this.description = description;
            this.formula = formula;
            this.result = result;
            this.unit = unit;
        }
        
        // Getters and Setters
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public String getFormula() { return formula; }
        public void setFormula(String formula) { this.formula = formula; }
        
        public double getResult() { return result; }
        public void setResult(double result) { this.result = result; }
        
        public String getUnit() { return unit; }
        public void setUnit(String unit) { this.unit = unit; }
        
        @Override
        public String toString() {
            return String.format("%s: %s = %.6f %s", description, formula, result, unit);
        }
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("Part 6 Assessment Results\n");
        sb.append("========================\n");
        sb.append(String.format("Assessment Level: %s\n", assessmentLevel));
        sb.append(String.format("Component Type: %s (Valid: %s)\n", componentType, componentTypeValid));
        sb.append(String.format("Max Pit Depth: %.6f in\n", maxPitDepth));
        sb.append(String.format("Min Remaining Thickness: %.6f in\n", minRemainingThickness));
        sb.append(String.format("Pit Density: %.6f pits/in²\n", pitDensity));
        sb.append(String.format("Required Thickness: %.6f in\n", treq));
        sb.append(String.format("RSF: %.6f\n", rsf));
        sb.append(String.format("MAWP: %.6f psi\n", mawp));
        sb.append(String.format("Severity: %s\n", getPittingSeverity()));
        sb.append(String.format("Result: %s\n", getAssessmentResult()));
        
        if (!notes.isEmpty()) {
            sb.append("\nNotes:\n");
            notes.forEach(note -> sb.append("- ").append(note).append("\n"));
        }
        
        if (!errors.isEmpty()) {
            sb.append("\nErrors:\n");
            errors.forEach(error -> sb.append("- ").append(error).append("\n"));
        }
        
        return sb.toString();
    }
}
