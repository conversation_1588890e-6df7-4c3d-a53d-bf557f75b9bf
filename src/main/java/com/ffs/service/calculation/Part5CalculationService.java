package com.ffs.service.calculation;

import com.ffs.domain.Assessment;
import com.ffs.domain.Material;
import com.ffs.domain.ThicknessReading;
import com.ffs.service.calculation.dto.Part5Result;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Part 5 Local Thin Areas (LTA) Calculation Service
 * Implements API 579-1/ASME FFS-1 Part 5 assessment procedures
 */
@Service
public class Part5CalculationService {
    
    private static final double RSF_A = 0.9; // Remaining Strength Factor for Level 2
    private static final int PRECISION = 6;
    
    /**
     * Perform Level 1 Assessment for Local Thin Areas
     * @param assessment Assessment data
     * @return Part 5 Level 1 results
     */
    public Part5Result performLevel1Assessment(Assessment assessment) {
        Part5Result result = new Part5Result();
        result.setAssessmentLevel(Assessment.AssessmentLevel.LEVEL_1);
        
        try {
            // Step 1: Validate component type and LTA criteria
            validateComponentType(assessment, result);
            if (!result.isComponentTypeValid()) {
                return result;
            }
            
            // Step 2: Get thickness readings and determine critical dimensions
            List<ThicknessReading> validReadings = getValidThicknessReadings(assessment);
            if (validReadings.isEmpty()) {
                result.addError("No valid thickness readings found");
                return result;
            }
            
            double tmm = calculateMinimumMeasuredThickness(validReadings);
            double tam = calculateAverageMeasuredThickness(validReadings);
            
            result.setTmm(round(tmm));
            result.setTam(round(tam));
            
            // Step 3: Calculate required thickness using design code
            double treq = calculateRequiredThickness(assessment);
            result.setTreq(round(treq));
            
            // Step 4: Calculate assessment length for screening
            double assessmentLength = calculateAssessmentLength(assessment);
            result.setAssessmentLength(round(assessmentLength));
            
            // Step 5: Determine LTA dimensions (s and c)
            double s = calculateLongitudinalExtent(validReadings);
            double c = calculateCircumferentialExtent(validReadings);
            result.setLongitudinalExtent(round(s));
            result.setCircumferentialExtent(round(c));
            
            // Step 6: Check limiting flaw size criteria
            boolean limitingCriteriaMet = checkLimitingFlawSizeCriteria(assessment, tmm, s, result);
            result.setLimitingCriteriaMet(limitingCriteriaMet);
            
            if (!limitingCriteriaMet) {
                result.addNote("Limiting flaw size criteria not met - Level 2 assessment required");
                return result;
            }
            
            // Step 7: Calculate MAWP for the component
            double mawp = calculateLocalAreaMAWP(assessment, tmm);
            result.setMawp(round(mawp));
            
            // Step 8: Calculate RSF
            double rsf = calculateRSF(tmm, treq, assessment);
            result.setRsf(round(rsf));
            
            // Step 9: Check acceptance criteria
            boolean acceptable = rsf >= RSF_A;
            result.setAcceptable(acceptable);
            
            // Check MAWP against design pressure
            if (assessment.getDesignPressure() != null) {
                boolean mawpAcceptable = mawp >= assessment.getDesignPressure();
                result.setMawpAcceptable(mawpAcceptable);
                result.setAcceptable(acceptable && mawpAcceptable);
            }
            
            // Calculate remaining life if corrosion rate is provided
            if (assessment.getCorrosionRate() != null && assessment.getCorrosionRate() > 0) {
                double remainingLife = calculateRemainingLife(tmm, treq, assessment.getCorrosionRate());
                result.setRemainingLife(round(remainingLife));
            }
            
        } catch (Exception e) {
            result.addError("Calculation error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * Perform Level 2 Assessment for Local Thin Areas
     * @param assessment Assessment data
     * @return Part 5 Level 2 results
     */
    public Part5Result performLevel2Assessment(Assessment assessment) {
        Part5Result result = performLevel1Assessment(assessment);
        result.setAssessmentLevel(Assessment.AssessmentLevel.LEVEL_2);
        
        if (!result.isComponentTypeValid()) {
            return result;
        }
        
        try {
            // Level 2 uses more detailed RSF calculations
            // This would include subsection analysis and Folias factors
            // For now, use simplified approach with RSF_A = 0.9
            
            double rsf = result.getRsf();
            boolean acceptableLevel2 = rsf >= RSF_A;
            result.setAcceptableLevel2(acceptableLevel2);
            
            if (!acceptableLevel2) {
                // Calculate reduced MAWP
                double mawpReduced = result.getMawp() * (rsf / RSF_A);
                result.setMawpReduced(round(mawpReduced));
            }
            
        } catch (Exception e) {
            result.addError("Level 2 calculation error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * Validate component type for Part 5 assessment
     */
    private void validateComponentType(Assessment assessment, Part5Result result) {
        // Similar to Part 4 but with specific Part 5 criteria
        try {
            if (assessment.getVesselHeight() != null && assessment.getInsideDiameter() != null) {
                double hOverD = assessment.getVesselHeight() / assessment.getInsideDiameter();
                boolean criteria1 = hOverD <= 3.0;
                boolean criteria2 = assessment.getVesselHeight() <= 1200.0; // 100 ft in inches
                
                result.setComponentType("Type A");
                result.setComponentTypeValid(criteria1 && criteria2);
                
                result.addNote(String.format("H/D = %.3f (≤ 3.0: %s)", hOverD, criteria1 ? "PASSED" : "FAILED"));
                result.addNote(String.format("H = %.1f in (≤ 1200 in: %s)", 
                    assessment.getVesselHeight(), criteria2 ? "PASSED" : "FAILED"));
            } else {
                result.setComponentType("Unknown");
                result.setComponentTypeValid(false);
                result.addError("Insufficient geometry data for component classification");
            }
        } catch (Exception e) {
            result.setComponentTypeValid(false);
            result.addError("Component type validation error: " + e.getMessage());
        }
    }
    
    /**
     * Get valid thickness readings for calculations
     */
    private List<ThicknessReading> getValidThicknessReadings(Assessment assessment) {
        return assessment.getThicknessReadings().stream()
            .filter(ThicknessReading::isValidForCalculations)
            .collect(Collectors.toList());
    }
    
    /**
     * Calculate minimum measured thickness (tmm)
     */
    private double calculateMinimumMeasuredThickness(List<ThicknessReading> readings) {
        return readings.stream()
            .mapToDouble(ThicknessReading::getThickness)
            .min()
            .orElse(0.0);
    }
    
    /**
     * Calculate average measured thickness (tam)
     */
    private double calculateAverageMeasuredThickness(List<ThicknessReading> readings) {
        return readings.stream()
            .mapToDouble(ThicknessReading::getThickness)
            .average()
            .orElse(0.0);
    }
    
    /**
     * Calculate required thickness from design code
     * Formula: t_req = (P * R) / (S * E - 0.6 * P)
     */
    private double calculateRequiredThickness(Assessment assessment) {
        double pressure = assessment.getDesignPressure();
        double radius = assessment.getInsideDiameter() / 2.0;
        double allowableStress = getAllowableStress(assessment);
        double weldEfficiency = assessment.getCircumferentialWeldEfficiency();
        
        return (pressure * radius) / (allowableStress * weldEfficiency - 0.6 * pressure);
    }
    
    /**
     * Calculate assessment length for screening defect size
     * Formula: L = sqrt(R * t)
     */
    private double calculateAssessmentLength(Assessment assessment) {
        double radius = assessment.getInsideDiameter() / 2.0;
        double thickness = assessment.getNominalThickness();
        
        return Math.sqrt(radius * thickness);
    }
    
    /**
     * Calculate local area MAWP
     * Formula: MAWP = (2 * S * E * (t_min - CA)) / R
     */
    private double calculateLocalAreaMAWP(Assessment assessment, double thickness) {
        double radius = assessment.getInsideDiameter() / 2.0;
        double allowableStress = getAllowableStress(assessment);
        double weldEfficiency = assessment.getCircumferentialWeldEfficiency();
        double fca = assessment.getFutureCorrosionAllowance() != null ? 
                    assessment.getFutureCorrosionAllowance() : 0.0;
        
        double effectiveThickness = thickness - fca;
        return (2 * allowableStress * weldEfficiency * effectiveThickness) / radius;
    }
    
    /**
     * Calculate Remaining Strength Factor (RSF)
     * Formula: RSF = MA_measured / MA_required
     */
    private double calculateRSF(double measuredThickness, double requiredThickness, Assessment assessment) {
        double fca = assessment.getFutureCorrosionAllowance() != null ? 
                    assessment.getFutureCorrosionAllowance() : 0.0;
        
        double effectiveThickness = measuredThickness - fca;
        
        if (requiredThickness <= 0) return 0.0;
        return effectiveThickness / requiredThickness;
    }
    
    /**
     * Calculate longitudinal extent of LTA
     */
    private double calculateLongitudinalExtent(List<ThicknessReading> readings) {
        // Simplified calculation - would need actual coordinate analysis
        return readings.size() * 2.0; // Assume 2" spacing
    }
    
    /**
     * Calculate circumferential extent of LTA
     */
    private double calculateCircumferentialExtent(List<ThicknessReading> readings) {
        // Simplified calculation - would need actual coordinate analysis
        return 2.0; // Default circumferential extent
    }
    
    /**
     * Check limiting flaw size criteria for Level 1 assessment
     */
    private boolean checkLimitingFlawSizeCriteria(Assessment assessment, double tmm, double s, Part5Result result) {
        double fca = assessment.getFutureCorrosionAllowance() != null ? 
                    assessment.getFutureCorrosionAllowance() : 0.0;
        
        // Criteria from API 579-1 Part 5
        boolean criteria1 = (tmm - fca) >= 0.1; // Minimum 0.1" remaining
        boolean criteria2 = s <= calculateAssessmentLength(assessment); // Length criteria
        
        result.addNote(String.format("Remaining thickness ≥ 0.1 in: %s", criteria1 ? "PASSED" : "FAILED"));
        result.addNote(String.format("Length criteria: %s", criteria2 ? "PASSED" : "FAILED"));
        
        return criteria1 && criteria2;
    }
    
    /**
     * Calculate remaining life based on corrosion rate
     * Formula: RL = (t_ml - t_FCA) / CR
     */
    private double calculateRemainingLife(double currentThickness, double requiredThickness, double corrosionRate) {
        double availableThickness = currentThickness - requiredThickness;
        return Math.max(0, availableThickness / corrosionRate);
    }
    
    /**
     * Get allowable stress for material at design temperature
     */
    private double getAllowableStress(Assessment assessment) {
        Material material = assessment.getMaterial();
        if (material != null && assessment.getDesignTemperature() != null) {
            Double stress = material.getAllowableStress(assessment.getDesignTemperature());
            if (stress != null) {
                return stress;
            }
        }
        // Default allowable stress if not found in material database
        return 17500.0; // psi for typical carbon steel
    }
    
    /**
     * Round value to specified precision
     */
    private double round(double value) {
        return BigDecimal.valueOf(value)
            .setScale(PRECISION, RoundingMode.HALF_UP)
            .doubleValue();
    }
}
