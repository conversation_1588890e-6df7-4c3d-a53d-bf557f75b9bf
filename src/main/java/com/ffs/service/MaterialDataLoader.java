package com.ffs.service;

import com.ffs.domain.Material;
import com.ffs.domain.MaterialProperty;
import com.ffs.repository.MaterialRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Material data loader to populate the database with standard materials
 * Based on the material data provided in the examples
 */
@Component
public class MaterialDataLoader implements CommandLineRunner {
    
    @Autowired
    private MaterialRepository materialRepository;
    
    @Override
    @Transactional
    public void run(String... args) throws Exception {
        if (materialRepository.count() == 0) {
            loadMaterialData();
        }
    }
    
    private void loadMaterialData() {
        // SA-516 Grade 60 (from Part 4 Example 1)
        Material sa516Grade60 = createMaterial(
            "SA-516", "Grade 60", 2007, "Carbon Steel",
            "Normalized and tempered or water-quenched and tempered",
            Material.FatigueCurve.B, -20.0, 650.0, false, false
        );
        
        // Add allowable stress at 200°F (118 MPa = 17,107 psi)
        addMaterialProperty(sa516Grade60, Material.PropertyType.ALLOWABLE_STRESS, 200.0, 17107.0, "psi", "ASME SEC II-D");
        addMaterialProperty(sa516Grade60, Material.PropertyType.ALLOWABLE_STRESS, 70.0, 17500.0, "psi", "ASME SEC II-D");
        addMaterialProperty(sa516Grade60, Material.PropertyType.YIELD_STRENGTH, 70.0, 32000.0, "psi", "ASME SEC II-D");
        addMaterialProperty(sa516Grade60, Material.PropertyType.TENSILE_STRENGTH, 70.0, 60000.0, "psi", "ASME SEC II-D");
        
        materialRepository.save(sa516Grade60);
        
        // SA-516 Grade 70 (from Part 5 and Part 6 Examples)
        Material sa516Grade70 = createMaterial(
            "SA-516", "Grade 70", 1992, "Carbon Steel",
            "Normalized and tempered or water-quenched and tempered",
            Material.FatigueCurve.B, -20.0, 650.0, false, false
        );
        
        // Add allowable stress properties
        addMaterialProperty(sa516Grade70, Material.PropertyType.ALLOWABLE_STRESS, 70.0, 20000.0, "psi", "ASME SEC II-D");
        addMaterialProperty(sa516Grade70, Material.PropertyType.ALLOWABLE_STRESS, 250.0, 17500.0, "psi", "ASME SEC II-D");
        addMaterialProperty(sa516Grade70, Material.PropertyType.ALLOWABLE_STRESS, 450.0, 15000.0, "psi", "ASME SEC II-D");
        addMaterialProperty(sa516Grade70, Material.PropertyType.ALLOWABLE_STRESS, 650.0, 12000.0, "psi", "ASME SEC II-D");
        addMaterialProperty(sa516Grade70, Material.PropertyType.YIELD_STRENGTH, 70.0, 38000.0, "psi", "ASME SEC II-D");
        addMaterialProperty(sa516Grade70, Material.PropertyType.TENSILE_STRENGTH, 70.0, 70000.0, "psi", "ASME SEC II-D");
        
        materialRepository.save(sa516Grade70);
        
        // SA-516 Grade 65
        Material sa516Grade65 = createMaterial(
            "SA-516", "Grade 65", 1968, "Carbon Steel",
            "Normalized and tempered or water-quenched and tempered",
            Material.FatigueCurve.B, -20.0, 650.0, false, false
        );
        
        addMaterialProperty(sa516Grade65, Material.PropertyType.ALLOWABLE_STRESS, 70.0, 120.7 * 145.038, "psi", "ASME SEC II-D"); // 120.7 MPa
        addMaterialProperty(sa516Grade65, Material.PropertyType.YIELD_STRENGTH, 70.0, 35000.0, "psi", "ASME SEC II-D");
        addMaterialProperty(sa516Grade65, Material.PropertyType.TENSILE_STRENGTH, 70.0, 65000.0, "psi", "ASME SEC II-D");
        
        materialRepository.save(sa516Grade65);
        
        // SA-285 Grade A/B (Curve B materials)
        Material sa285GradeA = createMaterial(
            "SA-285", "Grade A", 1992, "Carbon Steel",
            "Hot-rolled plates",
            Material.FatigueCurve.B, -20.0, 650.0, false, false
        );
        
        addMaterialProperty(sa285GradeA, Material.PropertyType.ALLOWABLE_STRESS, 70.0, 13750.0, "psi", "ASME SEC II-D");
        addMaterialProperty(sa285GradeA, Material.PropertyType.YIELD_STRENGTH, 70.0, 30000.0, "psi", "ASME SEC II-D");
        addMaterialProperty(sa285GradeA, Material.PropertyType.TENSILE_STRENGTH, 70.0, 55000.0, "psi", "ASME SEC II-D");
        
        materialRepository.save(sa285GradeA);
        
        // SA-515 Grade 60 (Curve B)
        Material sa515Grade60 = createMaterial(
            "SA-515", "Grade 60", 1992, "Carbon Steel",
            "Normalized",
            Material.FatigueCurve.B, -20.0, 650.0, false, true
        );
        
        addMaterialProperty(sa515Grade60, Material.PropertyType.ALLOWABLE_STRESS, 70.0, 17500.0, "psi", "ASME SEC II-D");
        addMaterialProperty(sa515Grade60, Material.PropertyType.YIELD_STRENGTH, 70.0, 32000.0, "psi", "ASME SEC II-D");
        addMaterialProperty(sa515Grade60, Material.PropertyType.TENSILE_STRENGTH, 70.0, 60000.0, "psi", "ASME SEC II-D");
        
        materialRepository.save(sa515Grade60);
        
        // SA-537 Class 1 (Curve D - high quality)
        Material sa537Class1 = createMaterial(
            "SA-537", "Class 1", 1992, "Carbon Steel",
            "Normalized",
            Material.FatigueCurve.D, -50.0, 650.0, true, true
        );
        
        addMaterialProperty(sa537Class1, Material.PropertyType.ALLOWABLE_STRESS, 70.0, 20000.0, "psi", "ASME SEC II-D");
        addMaterialProperty(sa537Class1, Material.PropertyType.YIELD_STRENGTH, 70.0, 50000.0, "psi", "ASME SEC II-D");
        addMaterialProperty(sa537Class1, Material.PropertyType.TENSILE_STRENGTH, 70.0, 70000.0, "psi", "ASME SEC II-D");
        
        materialRepository.save(sa537Class1);
        
        System.out.println("Material database loaded with " + materialRepository.count() + " materials");
    }
    
    private Material createMaterial(String specification, String grade, Integer yearEdition,
                                  String materialType, String conditionDescription,
                                  Material.FatigueCurve fatigueCurve, Double minTemp, Double maxTemp,
                                  boolean isFineGrain, boolean isNormalized) {
        Material material = new Material(specification, grade, fatigueCurve);
        material.setYearEdition(yearEdition);
        material.setMaterialType(materialType);
        material.setConditionDescription(conditionDescription);
        material.setMinTemperature(minTemp);
        material.setMaxTemperature(maxTemp);
        material.setIsFineGrainPractice(isFineGrain);
        material.setIsNormalized(isNormalized);
        material.setIsObsolete(false);
        
        return material;
    }
    
    private void addMaterialProperty(Material material, Material.PropertyType propertyType,
                                   Double temperature, Double value, String unit, String source) {
        MaterialProperty property = new MaterialProperty(material, propertyType, temperature, value, unit);
        property.setSource(source);
        material.getProperties().add(property);
    }
}
