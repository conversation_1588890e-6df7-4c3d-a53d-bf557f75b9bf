package com.ffs.repository;

import com.ffs.domain.Assessment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * Repository interface for Assessment entity
 */
@Repository
public interface AssessmentRepository extends JpaRepository<Assessment, Long> {
    
    /**
     * Find assessments by status
     */
    List<Assessment> findByStatus(Assessment.AssessmentStatus status);
    
    /**
     * Find assessments by assessment type
     */
    List<Assessment> findByAssessmentType(Assessment.AssessmentType assessmentType);
    
    /**
     * Find assessments by component type
     */
    List<Assessment> findByComponentTypeContainingIgnoreCase(String componentType);
    
    /**
     * Find assessments by status and acceptability
     */
    List<Assessment> findByStatusAndIsAcceptable(Assessment.AssessmentStatus status, Boolean isAcceptable);
    
    /**
     * Count assessments by status
     */
    long countByStatus(Assessment.AssessmentStatus status);
    
    /**
     * Count assessments by assessment type
     */
    long countByAssessmentType(Assessment.AssessmentType assessmentType);
    
    /**
     * Count assessments by acceptability
     */
    long countByIsAcceptable(Boolean isAcceptable);
    
    /**
     * Find assessments by created by user
     */
    List<Assessment> findByCreatedByIgnoreCase(String createdBy);
    
    /**
     * Find assessments by component ID
     */
    List<Assessment> findByComponentIdContainingIgnoreCase(String componentId);
    
    /**
     * Find assessments by location
     */
    List<Assessment> findByLocationContainingIgnoreCase(String location);
    
    /**
     * Find assessments requiring attention (completed but not acceptable)
     */
    @Query("SELECT a FROM Assessment a WHERE a.status = 'COMPLETED' AND (a.isAcceptable = false OR a.isAcceptable IS NULL)")
    List<Assessment> findAssessmentsRequiringAttention();
    
    /**
     * Find assessments by material
     */
    @Query("SELECT a FROM Assessment a WHERE a.material.id = :materialId")
    List<Assessment> findByMaterialId(@Param("materialId") Long materialId);
    
    /**
     * Find assessments with MAWP below design pressure
     */
    @Query("SELECT a FROM Assessment a WHERE a.mawpReduced IS NOT NULL AND a.designPressure IS NOT NULL AND a.mawpReduced < a.designPressure")
    List<Assessment> findAssessmentsWithReducedMAWP();
    
    /**
     * Find assessments by multiple criteria
     */
    @Query("SELECT a FROM Assessment a WHERE " +
           "(:componentType IS NULL OR LOWER(a.componentType) LIKE LOWER(CONCAT('%', :componentType, '%'))) AND " +
           "(:assessmentType IS NULL OR a.assessmentType = :assessmentType) AND " +
           "(:status IS NULL OR a.status = :status)")
    List<Assessment> findByCriteria(@Param("componentType") String componentType,
                                   @Param("assessmentType") Assessment.AssessmentType assessmentType,
                                   @Param("status") Assessment.AssessmentStatus status);
    
    /**
     * Find assessments with remaining life below threshold
     */
    @Query("SELECT a FROM Assessment a WHERE a.remainingLife IS NOT NULL AND a.remainingLife < :threshold")
    List<Assessment> findAssessmentsWithLowRemainingLife(@Param("threshold") Double threshold);
    
    /**
     * Find recent assessments (last N days)
     */
    @Query("SELECT a FROM Assessment a WHERE a.createdAt >= CURRENT_DATE - :days")
    List<Assessment> findRecentAssessments(@Param("days") int days);
    
    /**
     * Get assessment summary statistics
     */
    @Query("SELECT " +
           "COUNT(a) as total, " +
           "SUM(CASE WHEN a.status = 'COMPLETED' THEN 1 ELSE 0 END) as completed, " +
           "SUM(CASE WHEN a.isAcceptable = true THEN 1 ELSE 0 END) as acceptable, " +
           "SUM(CASE WHEN a.assessmentType = 'PART_4_GENERAL_METAL_LOSS' THEN 1 ELSE 0 END) as part4, " +
           "SUM(CASE WHEN a.assessmentType = 'PART_5_LOCAL_THIN_AREAS' THEN 1 ELSE 0 END) as part5, " +
           "SUM(CASE WHEN a.assessmentType = 'PART_6_PITTING_CORROSION' THEN 1 ELSE 0 END) as part6 " +
           "FROM Assessment a")
    Object[] getAssessmentStatistics();
    
    /**
     * Find assessments by design pressure range
     */
    @Query("SELECT a FROM Assessment a WHERE a.designPressure BETWEEN :minPressure AND :maxPressure")
    List<Assessment> findByDesignPressureRange(@Param("minPressure") Double minPressure, 
                                              @Param("maxPressure") Double maxPressure);
    
    /**
     * Find assessments by design temperature range
     */
    @Query("SELECT a FROM Assessment a WHERE a.designTemperature BETWEEN :minTemp AND :maxTemp")
    List<Assessment> findByDesignTemperatureRange(@Param("minTemp") Double minTemp, 
                                                 @Param("maxTemp") Double maxTemp);
    
    /**
     * Find assessments by thickness range
     */
    @Query("SELECT a FROM Assessment a WHERE a.nominalThickness BETWEEN :minThickness AND :maxThickness")
    List<Assessment> findByThicknessRange(@Param("minThickness") Double minThickness, 
                                         @Param("maxThickness") Double maxThickness);
    
    /**
     * Find assessments by diameter range
     */
    @Query("SELECT a FROM Assessment a WHERE a.insideDiameter BETWEEN :minDiameter AND :maxDiameter")
    List<Assessment> findByDiameterRange(@Param("minDiameter") Double minDiameter, 
                                        @Param("maxDiameter") Double maxDiameter);
}
