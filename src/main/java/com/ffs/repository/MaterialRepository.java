package com.ffs.repository;

import com.ffs.domain.Material;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Material entity
 */
@Repository
public interface MaterialRepository extends JpaRepository<Material, Long> {
    
    /**
     * Find material by specification and grade
     */
    Optional<Material> findBySpecificationAndGrade(String specification, String grade);
    
    /**
     * Find materials by specification
     */
    List<Material> findBySpecificationContainingIgnoreCase(String specification);
    
    /**
     * Find materials by grade
     */
    List<Material> findByGradeContainingIgnoreCase(String grade);
    
    /**
     * Find materials by fatigue curve
     */
    List<Material> findByFatigueCurve(Material.FatigueCurve fatigueCurve);
    
    /**
     * Find materials by material type
     */
    List<Material> findByMaterialTypeContainingIgnoreCase(String materialType);
    
    /**
     * Find materials by year edition
     */
    List<Material> findByYearEdition(Integer yearEdition);
    
    /**
     * Find non-obsolete materials
     */
    List<Material> findByIsObsoleteFalse();
    
    /**
     * Find materials suitable for temperature range
     */
    @Query("SELECT m FROM Material m WHERE " +
           "(m.minTemperature IS NULL OR m.minTemperature <= :temperature) AND " +
           "(m.maxTemperature IS NULL OR m.maxTemperature >= :temperature)")
    List<Material> findSuitableForTemperature(@Param("temperature") Double temperature);
    
    /**
     * Find materials suitable for temperature range
     */
    @Query("SELECT m FROM Material m WHERE " +
           "(m.minTemperature IS NULL OR m.minTemperature <= :maxTemp) AND " +
           "(m.maxTemperature IS NULL OR m.maxTemperature >= :minTemp)")
    List<Material> findSuitableForTemperatureRange(@Param("minTemp") Double minTemp, 
                                                   @Param("maxTemp") Double maxTemp);
    
    /**
     * Find materials with fine grain practice
     */
    List<Material> findByIsFineGrainPracticeTrue();
    
    /**
     * Find normalized materials
     */
    List<Material> findByIsNormalizedTrue();
    
    /**
     * Search materials by multiple criteria
     */
    @Query("SELECT m FROM Material m WHERE " +
           "(:specification IS NULL OR LOWER(m.specification) LIKE LOWER(CONCAT('%', :specification, '%'))) AND " +
           "(:grade IS NULL OR LOWER(m.grade) LIKE LOWER(CONCAT('%', :grade, '%'))) AND " +
           "(:materialType IS NULL OR LOWER(m.materialType) LIKE LOWER(CONCAT('%', :materialType, '%'))) AND " +
           "(:fatigueCurve IS NULL OR m.fatigueCurve = :fatigueCurve) AND " +
           "(:includeObsolete = true OR m.isObsolete = false)")
    List<Material> searchMaterials(@Param("specification") String specification,
                                  @Param("grade") String grade,
                                  @Param("materialType") String materialType,
                                  @Param("fatigueCurve") Material.FatigueCurve fatigueCurve,
                                  @Param("includeObsolete") boolean includeObsolete);
    
    /**
     * Find materials with allowable stress data at specific temperature
     */
    @Query("SELECT DISTINCT m FROM Material m JOIN m.properties p WHERE " +
           "p.propertyType = 'ALLOWABLE_STRESS' AND " +
           "ABS(p.temperature - :temperature) < 0.1")
    List<Material> findWithAllowableStressAt(@Param("temperature") Double temperature);
    
    /**
     * Find materials by specification pattern (e.g., 'SA-516%')
     */
    @Query("SELECT m FROM Material m WHERE m.specification LIKE :pattern")
    List<Material> findBySpecificationPattern(@Param("pattern") String pattern);
    
    /**
     * Get all unique specifications
     */
    @Query("SELECT DISTINCT m.specification FROM Material m ORDER BY m.specification")
    List<String> findAllSpecifications();
    
    /**
     * Get all unique grades for a specification
     */
    @Query("SELECT DISTINCT m.grade FROM Material m WHERE m.specification = :specification ORDER BY m.grade")
    List<String> findGradesBySpecification(@Param("specification") String specification);
    
    /**
     * Get all unique material types
     */
    @Query("SELECT DISTINCT m.materialType FROM Material m ORDER BY m.materialType")
    List<String> findAllMaterialTypes();
    
    /**
     * Count materials by fatigue curve
     */
    long countByFatigueCurve(Material.FatigueCurve fatigueCurve);
    
    /**
     * Count non-obsolete materials
     */
    long countByIsObsoleteFalse();
    
    /**
     * Find recently added materials
     */
    @Query("SELECT m FROM Material m WHERE m.createdAt >= CURRENT_DATE - :days ORDER BY m.createdAt DESC")
    List<Material> findRecentlyAdded(@Param("days") int days);
    
    /**
     * Find materials with missing temperature data
     */
    @Query("SELECT m FROM Material m WHERE m.minTemperature IS NULL OR m.maxTemperature IS NULL")
    List<Material> findWithMissingTemperatureData();
    
    /**
     * Find materials with property data
     */
    @Query("SELECT DISTINCT m FROM Material m WHERE SIZE(m.properties) > 0")
    List<Material> findWithPropertyData();
    
    /**
     * Find materials without property data
     */
    @Query("SELECT m FROM Material m WHERE SIZE(m.properties) = 0")
    List<Material> findWithoutPropertyData();
}
