package com.ffs.controller.dto;

import com.ffs.domain.Assessment;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.util.List;

/**
 * Data Transfer Object for Assessment creation and update requests
 */
public class AssessmentRequest {
    
    @NotBlank(message = "Assessment name is required")
    private String name;
    
    private String description;
    
    @NotNull(message = "Assessment type is required")
    private Assessment.AssessmentType assessmentType;
    
    @NotNull(message = "Assessment level is required")
    private Assessment.AssessmentLevel assessmentLevel;
    
    // Component Information
    @NotBlank(message = "Component type is required")
    private String componentType;
    
    private String componentId;
    private String location;
    
    // Design Conditions
    @Positive(message = "Design pressure must be positive")
    private Double designPressure; // psi
    
    private Double designTemperature; // °F
    private Double operatingPressure; // psi
    private Double operatingTemperature; // °F
    
    // Geometry
    @Positive(message = "Inside diameter must be positive")
    private Double insideDiameter; // inches
    
    @Positive(message = "Nominal thickness must be positive")
    private Double nominalThickness; // inches
    
    private Double vesselHeight; // inches
    private Double vesselLength; // inches
    
    // Material and Corrosion
    private Long materialId;
    private Double uniformMetalLoss; // inches
    private Double futureCorrosionAllowance; // inches
    private Double corrosionRate; // inches/year
    
    // Weld Efficiency
    private Double longitudinalWeldEfficiency = 1.0;
    private Double circumferentialWeldEfficiency = 1.0;
    
    // Assessment Notes
    private String assessmentNotes;
    
    // Thickness readings for Parts 4 and 5
    private List<ThicknessReadingRequest> thicknessReadings;
    
    // Pitting data for Part 6
    private List<PittingDataRequest> pittingData;
    
    // Constructors
    public AssessmentRequest() {}
    
    // Getters and Setters
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public Assessment.AssessmentType getAssessmentType() { return assessmentType; }
    public void setAssessmentType(Assessment.AssessmentType assessmentType) { this.assessmentType = assessmentType; }
    
    public Assessment.AssessmentLevel getAssessmentLevel() { return assessmentLevel; }
    public void setAssessmentLevel(Assessment.AssessmentLevel assessmentLevel) { this.assessmentLevel = assessmentLevel; }
    
    public String getComponentType() { return componentType; }
    public void setComponentType(String componentType) { this.componentType = componentType; }
    
    public String getComponentId() { return componentId; }
    public void setComponentId(String componentId) { this.componentId = componentId; }
    
    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }
    
    public Double getDesignPressure() { return designPressure; }
    public void setDesignPressure(Double designPressure) { this.designPressure = designPressure; }
    
    public Double getDesignTemperature() { return designTemperature; }
    public void setDesignTemperature(Double designTemperature) { this.designTemperature = designTemperature; }
    
    public Double getOperatingPressure() { return operatingPressure; }
    public void setOperatingPressure(Double operatingPressure) { this.operatingPressure = operatingPressure; }
    
    public Double getOperatingTemperature() { return operatingTemperature; }
    public void setOperatingTemperature(Double operatingTemperature) { this.operatingTemperature = operatingTemperature; }
    
    public Double getInsideDiameter() { return insideDiameter; }
    public void setInsideDiameter(Double insideDiameter) { this.insideDiameter = insideDiameter; }
    
    public Double getNominalThickness() { return nominalThickness; }
    public void setNominalThickness(Double nominalThickness) { this.nominalThickness = nominalThickness; }
    
    public Double getVesselHeight() { return vesselHeight; }
    public void setVesselHeight(Double vesselHeight) { this.vesselHeight = vesselHeight; }
    
    public Double getVesselLength() { return vesselLength; }
    public void setVesselLength(Double vesselLength) { this.vesselLength = vesselLength; }
    
    public Long getMaterialId() { return materialId; }
    public void setMaterialId(Long materialId) { this.materialId = materialId; }
    
    public Double getUniformMetalLoss() { return uniformMetalLoss; }
    public void setUniformMetalLoss(Double uniformMetalLoss) { this.uniformMetalLoss = uniformMetalLoss; }
    
    public Double getFutureCorrosionAllowance() { return futureCorrosionAllowance; }
    public void setFutureCorrosionAllowance(Double futureCorrosionAllowance) { this.futureCorrosionAllowance = futureCorrosionAllowance; }
    
    public Double getCorrosionRate() { return corrosionRate; }
    public void setCorrosionRate(Double corrosionRate) { this.corrosionRate = corrosionRate; }
    
    public Double getLongitudinalWeldEfficiency() { return longitudinalWeldEfficiency; }
    public void setLongitudinalWeldEfficiency(Double longitudinalWeldEfficiency) { this.longitudinalWeldEfficiency = longitudinalWeldEfficiency; }
    
    public Double getCircumferentialWeldEfficiency() { return circumferentialWeldEfficiency; }
    public void setCircumferentialWeldEfficiency(Double circumferentialWeldEfficiency) { this.circumferentialWeldEfficiency = circumferentialWeldEfficiency; }
    
    public String getAssessmentNotes() { return assessmentNotes; }
    public void setAssessmentNotes(String assessmentNotes) { this.assessmentNotes = assessmentNotes; }
    
    public List<ThicknessReadingRequest> getThicknessReadings() { return thicknessReadings; }
    public void setThicknessReadings(List<ThicknessReadingRequest> thicknessReadings) { this.thicknessReadings = thicknessReadings; }
    
    public List<PittingDataRequest> getPittingData() { return pittingData; }
    public void setPittingData(List<PittingDataRequest> pittingData) { this.pittingData = pittingData; }
    
    /**
     * Nested class for thickness reading requests
     */
    public static class ThicknessReadingRequest {
        @NotNull
        private Integer sequenceNumber;
        private Double locationX;
        private Double locationY;
        private String locationDescription;
        
        @Positive
        @NotNull
        private Double thickness;
        
        private Double originalThickness;
        private String measurementMethod;
        private String inspector;
        private String notes;
        
        // Constructors
        public ThicknessReadingRequest() {}
        
        public ThicknessReadingRequest(Integer sequenceNumber, Double thickness) {
            this.sequenceNumber = sequenceNumber;
            this.thickness = thickness;
        }
        
        // Getters and Setters
        public Integer getSequenceNumber() { return sequenceNumber; }
        public void setSequenceNumber(Integer sequenceNumber) { this.sequenceNumber = sequenceNumber; }
        
        public Double getLocationX() { return locationX; }
        public void setLocationX(Double locationX) { this.locationX = locationX; }
        
        public Double getLocationY() { return locationY; }
        public void setLocationY(Double locationY) { this.locationY = locationY; }
        
        public String getLocationDescription() { return locationDescription; }
        public void setLocationDescription(String locationDescription) { this.locationDescription = locationDescription; }
        
        public Double getThickness() { return thickness; }
        public void setThickness(Double thickness) { this.thickness = thickness; }
        
        public Double getOriginalThickness() { return originalThickness; }
        public void setOriginalThickness(Double originalThickness) { this.originalThickness = originalThickness; }
        
        public String getMeasurementMethod() { return measurementMethod; }
        public void setMeasurementMethod(String measurementMethod) { this.measurementMethod = measurementMethod; }
        
        public String getInspector() { return inspector; }
        public void setInspector(String inspector) { this.inspector = inspector; }
        
        public String getNotes() { return notes; }
        public void setNotes(String notes) { this.notes = notes; }
    }
    
    /**
     * Nested class for pitting data requests
     */
    public static class PittingDataRequest {
        @NotNull
        private Integer pitCoupleId;
        
        @NotNull
        private Integer pitNumber;
        
        @Positive
        @NotNull
        private Double pitDepth;
        
        @Positive
        @NotNull
        private Double pitDiameter;
        
        private Double pitLength;
        private Double pitWidth;
        private Double distanceBetweenPits;
        private Double angularPosition;
        private Double axialPosition;
        private String pitShape;
        private String pitOrientation;
        private Double remainingThickness;
        private Double originalThickness;
        private String pittingGrade;
        private String measurementMethod;
        private String inspector;
        private String notes;
        
        // Constructors
        public PittingDataRequest() {}
        
        public PittingDataRequest(Integer pitCoupleId, Integer pitNumber, Double pitDepth, Double pitDiameter) {
            this.pitCoupleId = pitCoupleId;
            this.pitNumber = pitNumber;
            this.pitDepth = pitDepth;
            this.pitDiameter = pitDiameter;
        }
        
        // Getters and Setters
        public Integer getPitCoupleId() { return pitCoupleId; }
        public void setPitCoupleId(Integer pitCoupleId) { this.pitCoupleId = pitCoupleId; }
        
        public Integer getPitNumber() { return pitNumber; }
        public void setPitNumber(Integer pitNumber) { this.pitNumber = pitNumber; }
        
        public Double getPitDepth() { return pitDepth; }
        public void setPitDepth(Double pitDepth) { this.pitDepth = pitDepth; }
        
        public Double getPitDiameter() { return pitDiameter; }
        public void setPitDiameter(Double pitDiameter) { this.pitDiameter = pitDiameter; }
        
        public Double getPitLength() { return pitLength; }
        public void setPitLength(Double pitLength) { this.pitLength = pitLength; }
        
        public Double getPitWidth() { return pitWidth; }
        public void setPitWidth(Double pitWidth) { this.pitWidth = pitWidth; }
        
        public Double getDistanceBetweenPits() { return distanceBetweenPits; }
        public void setDistanceBetweenPits(Double distanceBetweenPits) { this.distanceBetweenPits = distanceBetweenPits; }
        
        public Double getAngularPosition() { return angularPosition; }
        public void setAngularPosition(Double angularPosition) { this.angularPosition = angularPosition; }
        
        public Double getAxialPosition() { return axialPosition; }
        public void setAxialPosition(Double axialPosition) { this.axialPosition = axialPosition; }
        
        public String getPitShape() { return pitShape; }
        public void setPitShape(String pitShape) { this.pitShape = pitShape; }
        
        public String getPitOrientation() { return pitOrientation; }
        public void setPitOrientation(String pitOrientation) { this.pitOrientation = pitOrientation; }
        
        public Double getRemainingThickness() { return remainingThickness; }
        public void setRemainingThickness(Double remainingThickness) { this.remainingThickness = remainingThickness; }
        
        public Double getOriginalThickness() { return originalThickness; }
        public void setOriginalThickness(Double originalThickness) { this.originalThickness = originalThickness; }
        
        public String getPittingGrade() { return pittingGrade; }
        public void setPittingGrade(String pittingGrade) { this.pittingGrade = pittingGrade; }
        
        public String getMeasurementMethod() { return measurementMethod; }
        public void setMeasurementMethod(String measurementMethod) { this.measurementMethod = measurementMethod; }
        
        public String getInspector() { return inspector; }
        public void setInspector(String inspector) { this.inspector = inspector; }
        
        public String getNotes() { return notes; }
        public void setNotes(String notes) { this.notes = notes; }
    }
}
