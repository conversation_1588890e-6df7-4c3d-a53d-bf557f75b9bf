package com.ffs.controller.dto;

import com.ffs.domain.Assessment;
import com.ffs.domain.ThicknessReading;
import com.ffs.domain.PittingData;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Data Transfer Object for Assessment responses
 */
public class AssessmentResponse {
    
    private Long id;
    private String name;
    private String description;
    private Assessment.AssessmentType assessmentType;
    private Assessment.AssessmentLevel assessmentLevel;
    private Assessment.AssessmentStatus status;
    
    // Component Information
    private String componentType;
    private String componentId;
    private String location;
    
    // Design Conditions
    private Double designPressure;
    private Double designTemperature;
    private Double operatingPressure;
    private Double operatingTemperature;
    
    // Geometry
    private Double insideDiameter;
    private Double nominalThickness;
    private Double vesselHeight;
    private Double vesselLength;
    
    // Material and Corrosion
    private String materialName;
    private Double uniformMetalLoss;
    private Double futureCorrosionAllowance;
    private Double corrosionRate;
    
    // Weld Efficiency
    private Double longitudinalWeldEfficiency;
    private Double circumferentialWeldEfficiency;
    
    // Assessment Results
    private Boolean isAcceptable;
    private Double mawp;
    private Double mawpReduced;
    private Double remainingLife;
    private Double rsf;
    private String assessmentNotes;
    
    // Audit fields
    private String createdBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime assessedAt;
    
    // Data counts
    private Integer thicknessReadingCount;
    private Integer pittingDataCount;
    
    // Error handling
    private String errorMessage;
    private boolean hasError;
    
    // Constructors
    public AssessmentResponse() {}
    
    private AssessmentResponse(String errorMessage) {
        this.errorMessage = errorMessage;
        this.hasError = true;
    }
    
    // Factory methods
    public static AssessmentResponse fromAssessment(Assessment assessment) {
        AssessmentResponse response = new AssessmentResponse();
        
        response.id = assessment.getId();
        response.name = assessment.getName();
        response.description = assessment.getDescription();
        response.assessmentType = assessment.getAssessmentType();
        response.assessmentLevel = assessment.getAssessmentLevel();
        response.status = assessment.getStatus();
        
        response.componentType = assessment.getComponentType();
        response.componentId = assessment.getComponentId();
        response.location = assessment.getLocation();
        
        response.designPressure = assessment.getDesignPressure();
        response.designTemperature = assessment.getDesignTemperature();
        response.operatingPressure = assessment.getOperatingPressure();
        response.operatingTemperature = assessment.getOperatingTemperature();
        
        response.insideDiameter = assessment.getInsideDiameter();
        response.nominalThickness = assessment.getNominalThickness();
        response.vesselHeight = assessment.getVesselHeight();
        response.vesselLength = assessment.getVesselLength();
        
        if (assessment.getMaterial() != null) {
            response.materialName = assessment.getMaterial().getFullDesignation();
        }
        response.uniformMetalLoss = assessment.getUniformMetalLoss();
        response.futureCorrosionAllowance = assessment.getFutureCorrosionAllowance();
        response.corrosionRate = assessment.getCorrosionRate();
        
        response.longitudinalWeldEfficiency = assessment.getLongitudinalWeldEfficiency();
        response.circumferentialWeldEfficiency = assessment.getCircumferentialWeldEfficiency();
        
        response.isAcceptable = assessment.getIsAcceptable();
        response.mawp = assessment.getMawp();
        response.mawpReduced = assessment.getMawpReduced();
        response.remainingLife = assessment.getRemainingLife();
        response.rsf = assessment.getRsf();
        response.assessmentNotes = assessment.getAssessmentNotes();
        
        response.createdBy = assessment.getCreatedBy();
        response.createdAt = assessment.getCreatedAt();
        response.updatedAt = assessment.getUpdatedAt();
        response.assessedAt = assessment.getAssessedAt();
        
        response.thicknessReadingCount = assessment.getThicknessReadings().size();
        response.pittingDataCount = assessment.getPittingData().size();
        
        response.hasError = false;
        
        return response;
    }
    
    public static AssessmentResponse error(String errorMessage) {
        return new AssessmentResponse(errorMessage);
    }
    
    // Business methods
    public String getStatusDescription() {
        if (hasError) {
            return "Error: " + errorMessage;
        }
        
        switch (status) {
            case DRAFT:
                return "Draft - Not yet assessed";
            case IN_PROGRESS:
                return "Assessment in progress";
            case COMPLETED:
                if (isAcceptable != null) {
                    return isAcceptable ? "Completed - Acceptable" : "Completed - Not Acceptable";
                }
                return "Completed";
            case REVIEWED:
                return "Reviewed";
            case APPROVED:
                return "Approved";
            default:
                return status.toString();
        }
    }
    
    public String getAssessmentTypeDescription() {
        if (assessmentType == null) return null;
        
        switch (assessmentType) {
            case PART_4_GENERAL_METAL_LOSS:
                return "Part 4 - General Metal Loss";
            case PART_5_LOCAL_THIN_AREAS:
                return "Part 5 - Local Thin Areas";
            case PART_6_PITTING_CORROSION:
                return "Part 6 - Pitting Corrosion";
            default:
                return assessmentType.toString();
        }
    }
    
    public boolean isCompleted() {
        return status == Assessment.AssessmentStatus.COMPLETED ||
               status == Assessment.AssessmentStatus.REVIEWED ||
               status == Assessment.AssessmentStatus.APPROVED;
    }
    
    public boolean requiresAttention() {
        return isCompleted() && (isAcceptable == null || !isAcceptable);
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public Assessment.AssessmentType getAssessmentType() { return assessmentType; }
    public void setAssessmentType(Assessment.AssessmentType assessmentType) { this.assessmentType = assessmentType; }
    
    public Assessment.AssessmentLevel getAssessmentLevel() { return assessmentLevel; }
    public void setAssessmentLevel(Assessment.AssessmentLevel assessmentLevel) { this.assessmentLevel = assessmentLevel; }
    
    public Assessment.AssessmentStatus getStatus() { return status; }
    public void setStatus(Assessment.AssessmentStatus status) { this.status = status; }
    
    public String getComponentType() { return componentType; }
    public void setComponentType(String componentType) { this.componentType = componentType; }
    
    public String getComponentId() { return componentId; }
    public void setComponentId(String componentId) { this.componentId = componentId; }
    
    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }
    
    public Double getDesignPressure() { return designPressure; }
    public void setDesignPressure(Double designPressure) { this.designPressure = designPressure; }
    
    public Double getDesignTemperature() { return designTemperature; }
    public void setDesignTemperature(Double designTemperature) { this.designTemperature = designTemperature; }
    
    public Double getOperatingPressure() { return operatingPressure; }
    public void setOperatingPressure(Double operatingPressure) { this.operatingPressure = operatingPressure; }
    
    public Double getOperatingTemperature() { return operatingTemperature; }
    public void setOperatingTemperature(Double operatingTemperature) { this.operatingTemperature = operatingTemperature; }
    
    public Double getInsideDiameter() { return insideDiameter; }
    public void setInsideDiameter(Double insideDiameter) { this.insideDiameter = insideDiameter; }
    
    public Double getNominalThickness() { return nominalThickness; }
    public void setNominalThickness(Double nominalThickness) { this.nominalThickness = nominalThickness; }
    
    public Double getVesselHeight() { return vesselHeight; }
    public void setVesselHeight(Double vesselHeight) { this.vesselHeight = vesselHeight; }
    
    public Double getVesselLength() { return vesselLength; }
    public void setVesselLength(Double vesselLength) { this.vesselLength = vesselLength; }
    
    public String getMaterialName() { return materialName; }
    public void setMaterialName(String materialName) { this.materialName = materialName; }
    
    public Double getUniformMetalLoss() { return uniformMetalLoss; }
    public void setUniformMetalLoss(Double uniformMetalLoss) { this.uniformMetalLoss = uniformMetalLoss; }
    
    public Double getFutureCorrosionAllowance() { return futureCorrosionAllowance; }
    public void setFutureCorrosionAllowance(Double futureCorrosionAllowance) { this.futureCorrosionAllowance = futureCorrosionAllowance; }
    
    public Double getCorrosionRate() { return corrosionRate; }
    public void setCorrosionRate(Double corrosionRate) { this.corrosionRate = corrosionRate; }
    
    public Double getLongitudinalWeldEfficiency() { return longitudinalWeldEfficiency; }
    public void setLongitudinalWeldEfficiency(Double longitudinalWeldEfficiency) { this.longitudinalWeldEfficiency = longitudinalWeldEfficiency; }
    
    public Double getCircumferentialWeldEfficiency() { return circumferentialWeldEfficiency; }
    public void setCircumferentialWeldEfficiency(Double circumferentialWeldEfficiency) { this.circumferentialWeldEfficiency = circumferentialWeldEfficiency; }
    
    public Boolean getIsAcceptable() { return isAcceptable; }
    public void setIsAcceptable(Boolean isAcceptable) { this.isAcceptable = isAcceptable; }
    
    public Double getMawp() { return mawp; }
    public void setMawp(Double mawp) { this.mawp = mawp; }
    
    public Double getMawpReduced() { return mawpReduced; }
    public void setMawpReduced(Double mawpReduced) { this.mawpReduced = mawpReduced; }
    
    public Double getRemainingLife() { return remainingLife; }
    public void setRemainingLife(Double remainingLife) { this.remainingLife = remainingLife; }
    
    public Double getRsf() { return rsf; }
    public void setRsf(Double rsf) { this.rsf = rsf; }
    
    public String getAssessmentNotes() { return assessmentNotes; }
    public void setAssessmentNotes(String assessmentNotes) { this.assessmentNotes = assessmentNotes; }
    
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    public LocalDateTime getAssessedAt() { return assessedAt; }
    public void setAssessedAt(LocalDateTime assessedAt) { this.assessedAt = assessedAt; }
    
    public Integer getThicknessReadingCount() { return thicknessReadingCount; }
    public void setThicknessReadingCount(Integer thicknessReadingCount) { this.thicknessReadingCount = thicknessReadingCount; }
    
    public Integer getPittingDataCount() { return pittingDataCount; }
    public void setPittingDataCount(Integer pittingDataCount) { this.pittingDataCount = pittingDataCount; }
    
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    
    public boolean isHasError() { return hasError; }
    public void setHasError(boolean hasError) { this.hasError = hasError; }
}
