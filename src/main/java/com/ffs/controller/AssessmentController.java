package com.ffs.controller;

import com.ffs.domain.Assessment;
import com.ffs.service.AssessmentService;
import com.ffs.service.calculation.Part4CalculationService;
import com.ffs.service.calculation.Part5CalculationService;
import com.ffs.service.calculation.Part6CalculationService;
import com.ffs.service.calculation.dto.Part4Result;
import com.ffs.service.calculation.dto.Part5Result;
import com.ffs.service.calculation.dto.Part6Result;
import com.ffs.controller.dto.AssessmentRequest;
import com.ffs.controller.dto.AssessmentResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * REST Controller for FFS Assessment operations
 * Provides endpoints for creating, managing, and executing FFS assessments
 */
@RestController
@RequestMapping("/api/v1/assessments")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AssessmentController {
    
    @Autowired
    private AssessmentService assessmentService;
    
    @Autowired
    private Part4CalculationService part4CalculationService;

    @Autowired
    private Part5CalculationService part5CalculationService;

    @Autowired
    private Part6CalculationService part6CalculationService;
    
    /**
     * Create a new FFS assessment
     */
    @PostMapping
    public ResponseEntity<AssessmentResponse> createAssessment(@Valid @RequestBody AssessmentRequest request) {
        try {
            Assessment assessment = assessmentService.createAssessment(request);
            AssessmentResponse response = AssessmentResponse.fromAssessment(assessment);
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(AssessmentResponse.error("Failed to create assessment: " + e.getMessage()));
        }
    }
    
    /**
     * Get all assessments
     */
    @GetMapping
    public ResponseEntity<List<AssessmentResponse>> getAllAssessments() {
        try {
            List<Assessment> assessments = assessmentService.getAllAssessments();
            List<AssessmentResponse> responses = assessments.stream()
                .map(AssessmentResponse::fromAssessment)
                .toList();
            return ResponseEntity.ok(responses);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * Get assessment by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<AssessmentResponse> getAssessment(@PathVariable Long id) {
        try {
            Assessment assessment = assessmentService.getAssessmentById(id);
            if (assessment == null) {
                return ResponseEntity.notFound().build();
            }
            AssessmentResponse response = AssessmentResponse.fromAssessment(assessment);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(AssessmentResponse.error("Failed to retrieve assessment: " + e.getMessage()));
        }
    }
    
    /**
     * Update an existing assessment
     */
    @PutMapping("/{id}")
    public ResponseEntity<AssessmentResponse> updateAssessment(
            @PathVariable Long id, 
            @Valid @RequestBody AssessmentRequest request) {
        try {
            Assessment assessment = assessmentService.updateAssessment(id, request);
            if (assessment == null) {
                return ResponseEntity.notFound().build();
            }
            AssessmentResponse response = AssessmentResponse.fromAssessment(assessment);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(AssessmentResponse.error("Failed to update assessment: " + e.getMessage()));
        }
    }
    
    /**
     * Delete an assessment
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAssessment(@PathVariable Long id) {
        try {
            boolean deleted = assessmentService.deleteAssessment(id);
            if (!deleted) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * Execute Part 4 Level 1 Assessment
     */
    @PostMapping("/{id}/part4/level1")
    public ResponseEntity<Part4Result> executePart4Level1(@PathVariable Long id) {
        try {
            Assessment assessment = assessmentService.getAssessmentById(id);
            if (assessment == null) {
                return ResponseEntity.notFound().build();
            }
            
            if (assessment.getAssessmentType() != Assessment.AssessmentType.PART_4_GENERAL_METAL_LOSS) {
                return ResponseEntity.badRequest().build();
            }
            
            Part4Result result = part4CalculationService.performLevel1Assessment(assessment);
            
            // Update assessment with results
            assessment.setAssessmentLevel(Assessment.AssessmentLevel.LEVEL_1);
            assessment.setIsAcceptable(result.isAcceptable());
            assessment.setMawp(result.getMawp());
            assessment.setRemainingLife(result.getRemainingLife());
            assessment.complete();
            assessmentService.saveAssessment(assessment);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Part4Result errorResult = new Part4Result();
            errorResult.addError("Calculation failed: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }
    
    /**
     * Execute Part 4 Level 2 Assessment
     */
    @PostMapping("/{id}/part4/level2")
    public ResponseEntity<Part4Result> executePart4Level2(@PathVariable Long id) {
        try {
            Assessment assessment = assessmentService.getAssessmentById(id);
            if (assessment == null) {
                return ResponseEntity.notFound().build();
            }

            if (assessment.getAssessmentType() != Assessment.AssessmentType.PART_4_GENERAL_METAL_LOSS) {
                return ResponseEntity.badRequest().build();
            }

            Part4Result result = part4CalculationService.performLevel2Assessment(assessment);

            // Update assessment with results
            assessment.setAssessmentLevel(Assessment.AssessmentLevel.LEVEL_2);
            assessment.setIsAcceptable(result.isLevel2Acceptable());
            assessment.setMawp(result.getMawpLevel2());
            assessment.setMawpReduced(result.getMawpLevel2());
            assessment.setRemainingLife(result.getRemainingLifeLevel2());
            assessment.complete();
            assessmentService.saveAssessment(assessment);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Part4Result errorResult = new Part4Result();
            errorResult.addError("Calculation failed: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    /**
     * Execute Part 5 Level 1 Assessment
     */
    @PostMapping("/{id}/part5/level1")
    public ResponseEntity<Part5Result> executePart5Level1(@PathVariable Long id) {
        try {
            Assessment assessment = assessmentService.getAssessmentById(id);
            if (assessment == null) {
                return ResponseEntity.notFound().build();
            }

            if (assessment.getAssessmentType() != Assessment.AssessmentType.PART_5_LOCAL_THIN_AREAS) {
                return ResponseEntity.badRequest().build();
            }

            Part5Result result = part5CalculationService.performLevel1Assessment(assessment);

            // Update assessment with results
            assessment.setAssessmentLevel(Assessment.AssessmentLevel.LEVEL_1);
            assessment.setIsAcceptable(result.isAcceptable());
            assessment.setMawp(result.getMawp());
            assessment.setRsf(result.getRsf());
            assessment.setRemainingLife(result.getRemainingLife());
            assessment.complete();
            assessmentService.saveAssessment(assessment);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Part5Result errorResult = new Part5Result();
            errorResult.addError("Calculation failed: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    /**
     * Execute Part 5 Level 2 Assessment
     */
    @PostMapping("/{id}/part5/level2")
    public ResponseEntity<Part5Result> executePart5Level2(@PathVariable Long id) {
        try {
            Assessment assessment = assessmentService.getAssessmentById(id);
            if (assessment == null) {
                return ResponseEntity.notFound().build();
            }

            if (assessment.getAssessmentType() != Assessment.AssessmentType.PART_5_LOCAL_THIN_AREAS) {
                return ResponseEntity.badRequest().build();
            }

            Part5Result result = part5CalculationService.performLevel2Assessment(assessment);

            // Update assessment with results
            assessment.setAssessmentLevel(Assessment.AssessmentLevel.LEVEL_2);
            assessment.setIsAcceptable(result.isLevel2Acceptable());
            assessment.setMawp(result.getMawp());
            assessment.setMawpReduced(result.getMawpReduced());
            assessment.setRsf(result.getRsf());
            assessment.setRemainingLife(result.getRemainingLife());
            assessment.complete();
            assessmentService.saveAssessment(assessment);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Part5Result errorResult = new Part5Result();
            errorResult.addError("Calculation failed: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    /**
     * Execute Part 6 Level 1 Assessment
     */
    @PostMapping("/{id}/part6/level1")
    public ResponseEntity<Part6Result> executePart6Level1(@PathVariable Long id) {
        try {
            Assessment assessment = assessmentService.getAssessmentById(id);
            if (assessment == null) {
                return ResponseEntity.notFound().build();
            }

            if (assessment.getAssessmentType() != Assessment.AssessmentType.PART_6_PITTING_CORROSION) {
                return ResponseEntity.badRequest().build();
            }

            Part6Result result = part6CalculationService.performLevel1Assessment(assessment);

            // Update assessment with results
            assessment.setAssessmentLevel(Assessment.AssessmentLevel.LEVEL_1);
            assessment.setIsAcceptable(result.isAcceptable());
            assessment.setMawp(result.getMawp());
            assessment.setRsf(result.getRsf());
            assessment.setRemainingLife(result.getRemainingLife());
            assessment.complete();
            assessmentService.saveAssessment(assessment);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Part6Result errorResult = new Part6Result();
            errorResult.addError("Calculation failed: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    /**
     * Execute Part 6 Level 2 Assessment
     */
    @PostMapping("/{id}/part6/level2")
    public ResponseEntity<Part6Result> executePart6Level2(@PathVariable Long id) {
        try {
            Assessment assessment = assessmentService.getAssessmentById(id);
            if (assessment == null) {
                return ResponseEntity.notFound().build();
            }

            if (assessment.getAssessmentType() != Assessment.AssessmentType.PART_6_PITTING_CORROSION) {
                return ResponseEntity.badRequest().build();
            }

            Part6Result result = part6CalculationService.performLevel2Assessment(assessment);

            // Update assessment with results
            assessment.setAssessmentLevel(Assessment.AssessmentLevel.LEVEL_2);
            assessment.setIsAcceptable(result.isLevel2Acceptable());
            assessment.setMawp(result.getMawp());
            assessment.setMawpReduced(result.getMawpReduced());
            assessment.setRsf(result.getAverageRsf());
            assessment.setRemainingLife(result.getRemainingLife());
            assessment.complete();
            assessmentService.saveAssessment(assessment);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Part6Result errorResult = new Part6Result();
            errorResult.addError("Calculation failed: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }
    
    /**
     * Get assessment summary statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<AssessmentStatistics> getStatistics() {
        try {
            AssessmentStatistics stats = assessmentService.getStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * Search assessments by criteria
     */
    @GetMapping("/search")
    public ResponseEntity<List<AssessmentResponse>> searchAssessments(
            @RequestParam(required = false) String componentType,
            @RequestParam(required = false) Assessment.AssessmentType assessmentType,
            @RequestParam(required = false) Assessment.AssessmentStatus status) {
        try {
            List<Assessment> assessments = assessmentService.searchAssessments(componentType, assessmentType, status);
            List<AssessmentResponse> responses = assessments.stream()
                .map(AssessmentResponse::fromAssessment)
                .toList();
            return ResponseEntity.ok(responses);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * Inner class for assessment statistics
     */
    public static class AssessmentStatistics {
        private long totalAssessments;
        private long completedAssessments;
        private long acceptableAssessments;
        private long part4Assessments;
        private long part5Assessments;
        private long part6Assessments;
        
        // Constructors
        public AssessmentStatistics() {}
        
        public AssessmentStatistics(long total, long completed, long acceptable, 
                                  long part4, long part5, long part6) {
            this.totalAssessments = total;
            this.completedAssessments = completed;
            this.acceptableAssessments = acceptable;
            this.part4Assessments = part4;
            this.part5Assessments = part5;
            this.part6Assessments = part6;
        }
        
        // Getters and Setters
        public long getTotalAssessments() { return totalAssessments; }
        public void setTotalAssessments(long totalAssessments) { this.totalAssessments = totalAssessments; }
        
        public long getCompletedAssessments() { return completedAssessments; }
        public void setCompletedAssessments(long completedAssessments) { this.completedAssessments = completedAssessments; }
        
        public long getAcceptableAssessments() { return acceptableAssessments; }
        public void setAcceptableAssessments(long acceptableAssessments) { this.acceptableAssessments = acceptableAssessments; }
        
        public long getPart4Assessments() { return part4Assessments; }
        public void setPart4Assessments(long part4Assessments) { this.part4Assessments = part4Assessments; }
        
        public long getPart5Assessments() { return part5Assessments; }
        public void setPart5Assessments(long part5Assessments) { this.part5Assessments = part5Assessments; }
        
        public long getPart6Assessments() { return part6Assessments; }
        public void setPart6Assessments(long part6Assessments) { this.part6Assessments = part6Assessments; }
    }
}
