package com.ffs.service.calculation;

import com.ffs.domain.Assessment;
import com.ffs.domain.Material;
import com.ffs.domain.MaterialProperty;
import com.ffs.domain.ThicknessReading;
import com.ffs.service.calculation.dto.Part4Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for Part4CalculationService
 * Tests based on the provided API 579-1 examples
 */
@ExtendWith(MockitoExtension.class)
class Part4CalculationServiceTest {
    
    @InjectMocks
    private Part4CalculationService part4CalculationService;
    
    private Assessment testAssessment;
    private Material testMaterial;
    
    @BeforeEach
    void setUp() {
        // Create test material (SA-516 Grade 60)
        testMaterial = new Material("SA-516", "Grade 60", Material.FatigueCurve.B);
        testMaterial.setId(1L);
        testMaterial.setYearEdition(2007);
        testMaterial.setMaterialType("Carbon Steel");
        
        // Add allowable stress property at 200°F
        MaterialProperty allowableStress = new MaterialProperty(
            testMaterial, 
            Material.PropertyType.ALLOWABLE_STRESS, 
            200.0, // temperature in °F
            118.0 * 145.038, // 118 MPa converted to psi
            "psi"
        );
        allowableStress.setSource("ASME SEC II-D");
        testMaterial.getProperties().add(allowableStress);
        
        // Create test assessment based on Example 1
        testAssessment = createExample1Assessment();
    }
    
    /**
     * Create assessment based on Part 4 Example 1
     */
    private Assessment createExample1Assessment() {
        Assessment assessment = new Assessment();
        assessment.setId(1L);
        assessment.setName("Part 4 Example 1 - Horizontal Vessel");
        assessment.setAssessmentType(Assessment.AssessmentType.PART_4_GENERAL_METAL_LOSS);
        assessment.setAssessmentLevel(Assessment.AssessmentLevel.LEVEL_1);
        
        // Vessel data from example
        assessment.setDesignPressure(4.5 * 145.038); // 4.5 MPa to psi
        assessment.setDesignTemperature(200.0); // °F
        assessment.setInsideDiameter(484.0 / 25.4); // 484mm to inches
        assessment.setNominalThickness(16.0 / 25.4); // 16mm to inches
        assessment.setFutureCorrosionAllowance(2.5 / 25.4); // 2.5mm to inches
        assessment.setCircumferentialWeldEfficiency(1.0);
        assessment.setLongitudinalWeldEfficiency(1.0);
        assessment.setCorrosionRate(0.1 / 25.4); // 0.1mm/year to inches/year
        assessment.setVesselLength(1100.0 / 25.4); // 1100mm to inches (saddle distance)
        
        assessment.setMaterial(testMaterial);
        
        // Add thickness readings from example
        double[] thicknesses = {13, 12, 11, 12, 11, 12, 11, 12, 13, 13, 11, 12, 12, 13, 13};
        for (int i = 0; i < thicknesses.length; i++) {
            ThicknessReading reading = new ThicknessReading();
            reading.setAssessment(assessment);
            reading.setSequenceNumber(i + 1);
            reading.setThickness(thicknesses[i] / 25.4); // mm to inches
            reading.setOriginalThickness(assessment.getNominalThickness());
            assessment.getThicknessReadings().add(reading);
        }
        
        return assessment;
    }
    
    @Test
    void testLevel1Assessment_Example1() {
        // Execute Level 1 assessment
        Part4Result result = part4CalculationService.performLevel1Assessment(testAssessment);
        
        // Verify basic results
        assertNotNull(result);
        assertEquals(Assessment.AssessmentLevel.LEVEL_1, result.getAssessmentLevel());
        assertFalse(result.hasErrors());
        
        // Verify component type classification
        assertTrue(result.isComponentTypeValid());
        assertEquals("Type B", result.getComponentType());
        
        // Verify thickness calculations
        double expectedTmm = 11.0 / 25.4; // 11mm minimum thickness
        double expectedTam = 12.067 / 25.4; // Average thickness from example
        
        assertEquals(expectedTmm, result.getTmm(), 0.001);
        assertEquals(expectedTam, result.getTam(), 0.001);
        
        // Verify point thickness method is used
        assertTrue(result.isUsePointThicknessMethod());
        
        // Verify Level 1 assessment fails (as per example)
        assertFalse(result.isAcceptable());
        
        // Verify MAWP calculation
        assertTrue(result.getMawp() > 0);
        
        // Verify remaining life calculation
        assertTrue(result.getRemainingLife() > 0);
        
        System.out.println("Level 1 Assessment Results:");
        System.out.println(result.toString());
    }
    
    @Test
    void testLevel2Assessment_Example1() {
        // Execute Level 2 assessment
        Part4Result result = part4CalculationService.performLevel2Assessment(testAssessment);
        
        // Verify basic results
        assertNotNull(result);
        assertEquals(Assessment.AssessmentLevel.LEVEL_2, result.getAssessmentLevel());
        assertFalse(result.hasErrors());
        
        // Verify Level 2 specific calculations
        assertTrue(result.getTcminLevel2() > 0);
        assertTrue(result.getMawpLevel2() > 0);
        
        // Verify Level 2 assessment passes (as per example)
        assertTrue(result.isLevel2Acceptable());
        
        // Verify Level 2 remaining life
        assertTrue(result.getRemainingLifeLevel2() > result.getRemainingLife());
        
        System.out.println("Level 2 Assessment Results:");
        System.out.println(result.toString());
    }
    
    @Test
    void testComponentTypeValidation_TypeA() {
        // Create Type A component (vertical vessel)
        Assessment typeAAssessment = new Assessment();
        typeAAssessment.setInsideDiameter(60.0); // inches
        typeAAssessment.setVesselHeight(170.0); // inches (H/D = 2.83 < 3)
        typeAAssessment.setDesignPressure(300.0); // psi
        typeAAssessment.setNominalThickness(0.75); // inches
        typeAAssessment.setMaterial(testMaterial);
        typeAAssessment.setAssessmentType(Assessment.AssessmentType.PART_4_GENERAL_METAL_LOSS);
        
        // Add a thickness reading
        ThicknessReading reading = new ThicknessReading(typeAAssessment, 1, 0.7);
        typeAAssessment.getThicknessReadings().add(reading);
        
        Part4Result result = part4CalculationService.performLevel1Assessment(typeAAssessment);
        
        assertTrue(result.isComponentTypeValid());
        assertEquals("Type A", result.getComponentType());
    }
    
    @Test
    void testMinimumThicknessCalculation() {
        // Test minimum thickness calculation with known values
        Assessment assessment = new Assessment();
        assessment.setDesignPressure(300.0); // psi
        assessment.setInsideDiameter(60.0); // inches
        assessment.setCircumferentialWeldEfficiency(0.85);
        assessment.setMaterial(testMaterial);
        assessment.setAssessmentType(Assessment.AssessmentType.PART_4_GENERAL_METAL_LOSS);
        
        // Add thickness reading
        ThicknessReading reading = new ThicknessReading(assessment, 1, 0.5);
        assessment.getThicknessReadings().add(reading);
        
        Part4Result result = part4CalculationService.performLevel1Assessment(assessment);
        
        // Verify tcmin is calculated
        assertTrue(result.getTcmin() > 0);
        
        // Verify ASME formula: t = PR/(SE - 0.6P)
        double expectedTcmin = (300.0 * 30.0) / (17500.0 * 0.85 - 0.6 * 300.0);
        assertEquals(expectedTcmin, result.getTcmin(), 0.01);
    }
    
    @Test
    void testMAWPCalculation() {
        // Test MAWP calculation
        Assessment assessment = new Assessment();
        assessment.setInsideDiameter(60.0); // inches
        assessment.setCircumferentialWeldEfficiency(0.85);
        assessment.setMaterial(testMaterial);
        assessment.setAssessmentType(Assessment.AssessmentType.PART_4_GENERAL_METAL_LOSS);
        
        // Add thickness reading
        double thickness = 0.5; // inches
        ThicknessReading reading = new ThicknessReading(assessment, 1, thickness);
        assessment.getThicknessReadings().add(reading);
        
        Part4Result result = part4CalculationService.performLevel1Assessment(assessment);
        
        // Verify MAWP is calculated
        assertTrue(result.getMawp() > 0);
        
        // Verify ASME formula: P = SEt/(R + 0.6t)
        double radius = 30.0; // inches
        double expectedMAWP = (17500.0 * 0.85 * thickness) / (radius + 0.6 * thickness);
        assertEquals(expectedMAWP, result.getMawp(), 1.0);
    }
    
    @Test
    void testRemainingLifeCalculation() {
        // Test remaining life calculation
        Assessment assessment = new Assessment();
        assessment.setCorrosionRate(0.1); // inches/year
        assessment.setFutureCorrosionAllowance(0.1); // inches
        assessment.setDesignPressure(300.0);
        assessment.setInsideDiameter(60.0);
        assessment.setMaterial(testMaterial);
        assessment.setAssessmentType(Assessment.AssessmentType.PART_4_GENERAL_METAL_LOSS);
        
        // Add thickness reading
        ThicknessReading reading = new ThicknessReading(assessment, 1, 0.8);
        assessment.getThicknessReadings().add(reading);
        
        Part4Result result = part4CalculationService.performLevel1Assessment(assessment);
        
        // Verify remaining life is calculated
        assertTrue(result.getRemainingLife() > 0);
        
        System.out.println("Remaining Life: " + result.getRemainingLife() + " years");
    }
    
    @Test
    void testInvalidInputHandling() {
        // Test with missing thickness readings
        Assessment invalidAssessment = new Assessment();
        invalidAssessment.setAssessmentType(Assessment.AssessmentType.PART_4_GENERAL_METAL_LOSS);
        
        Part4Result result = part4CalculationService.performLevel1Assessment(invalidAssessment);
        
        assertTrue(result.hasErrors());
        assertFalse(result.isAcceptable());
    }
}
